// Absa Brand Colors
:root {
  --absa-red: #E31837;
  --absa-dark-blue: #003366;
  --absa-light-blue: #0066CC;
  --absa-gold: #FFB81C;
  --absa-green: #00A651;
  --absa-gray-light: #F5F5F5;
  --absa-gray-medium: #999999;
  --absa-gray-dark: #666666;
  --absa-white: #FFFFFF;
}

.edit-alert-modal {
  min-width: 500px;
  max-width: 600px;
  max-height: 90vh;

  @media (max-width: 599px) {
    min-width: 90vw;
    max-width: 95vw;
  }
}

h2[mat-dialog-title] {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--absa-dark-blue);
  margin-bottom: 1rem;
  font-size: 1.3rem;
  font-weight: 600;

  .severity-chip {
    margin-left: auto;
    font-size: 0.75rem;
    padding: 0.375rem 0.625rem;
    min-height: 28px;
  }

  @media (max-width: 599px) {
    font-size: 1.1rem;
    flex-wrap: wrap;
    
    .severity-chip {
      margin-left: 0;
      margin-top: 0.5rem;
    }
  }
}

.edit-alert-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
  padding: 0.5rem 0;
}

// Cards
.info-card,
.action-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;

  mat-card-header {
    background-color: var(--absa-gray-light);
    border-radius: 12px 12px 0 0;
    padding: 1rem;

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: var(--absa-dark-blue);
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0;
    }
  }

  mat-card-content {
    padding: 1.5rem;
  }
}

.info-card {
  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;

    @media (max-width: 599px) {
      grid-template-columns: 1fr;
      gap: 0.75rem;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem;
      background-color: var(--absa-gray-light);
      border-radius: 8px;

      label {
        font-weight: 500;
        color: var(--absa-gray-dark);
        font-size: 0.9rem;
      }

      .value {
        font-weight: 600;
        color: var(--absa-dark-blue);
        font-size: 1rem;

        &.denomination {
          display: flex;
          align-items: center;
          gap: 0.375rem;

          mat-icon {
            color: var(--absa-green);
            font-size: 18px;
          }
        }

        &.stock-critical {
          color: #CC0000;
          background-color: rgba(204, 0, 0, 0.1);
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
        }

        &.shortage {
          color: var(--absa-red);
          font-weight: 700;
        }
      }

      @media (max-width: 599px) {
        flex-direction: column;
        text-align: center;
        gap: 0.25rem;
      }
    }
  }
}

.action-card {
  &.add-stock-card {
    border-left: 4px solid var(--absa-green);
  }

  &.threshold-card {
    border-left: 4px solid var(--absa-light-blue);
  }

  .action-description {
    margin: 0 0 1rem 0;
    color: var(--absa-gray-dark);
    font-size: 0.95rem;
    line-height: 1.4;
  }

  .action-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .full-width {
      width: 100%;
    }

    .calculation-info,
    .threshold-info {
      background-color: var(--absa-gray-light);
      padding: 1rem;
      border-radius: 8px;
      border-left: 4px solid var(--absa-green);

      .calc-item,
      .threshold-change {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        &:last-child {
          margin-bottom: 0;
        }

        .calc-label,
        .change-label {
          font-weight: 500;
          color: var(--absa-gray-dark);
          font-size: 0.9rem;
        }

        .calc-value,
        .change-value {
          font-weight: 600;
          color: var(--absa-dark-blue);
          font-size: 1rem;

          &.increase {
            color: var(--absa-green);
          }

          &.decrease {
            color: var(--absa-red);
          }
        }
      }
    }

    .threshold-info {
      border-left-color: var(--absa-light-blue);
    }

    .action-button {
      align-self: flex-start;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
      }

      &:disabled {
        opacity: 0.6;
      }

      @media (max-width: 599px) {
        align-self: stretch;
        width: 100%;
      }
    }
  }
}

// Severity chip styles
mat-chip {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.375rem !important;
  border-radius: 16px !important;
  line-height: 1 !important;

  &.severity-critical {
    background-color: #CC0000 !important;
    color: white !important;
  }

  &.severity-high {
    background-color: var(--absa-red) !important;
    color: white !important;
  }

  &.severity-medium {
    background-color: var(--absa-gold) !important;
    color: var(--absa-dark-blue) !important;
  }

  &.severity-low {
    background-color: var(--absa-light-blue) !important;
    color: white !important;
  }

  mat-icon {
    font-size: 14px !important;
    width: 14px !important;
    height: 14px !important;
    margin: 0 !important;
  }
}

// Dialog actions
mat-dialog-actions {
  padding: 1rem 0 0 0;
  gap: 0.5rem;

  button {
    display: flex;
    align-items: center;
    gap: 0.375rem;
  }
}

// Divider
mat-divider {
  margin: 0.5rem 0;
  border-color: #e0e0e0;
}
