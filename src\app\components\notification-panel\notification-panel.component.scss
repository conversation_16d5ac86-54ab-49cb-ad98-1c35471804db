// Notification Trigger Button
.notification-trigger {
  position: relative;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  mat-icon {
    transition: color 0.2s ease;
  }
}

// Global notification menu styles - using ::ng-deep to override Material defaults
::ng-deep .notification-menu {
  .mat-mdc-menu-panel {
    max-width: 380px !important;
    min-width: 320px !important;
    border-radius: 4px !important;
    box-shadow: 0 2px 4px -1px rgba(0,0,0,.2), 0 4px 5px 0 rgba(0,0,0,.14), 0 1px 10px 0 rgba(0,0,0,.12) !important;
    overflow: hidden !important;
    margin-top: 4px !important;

    @media (max-width: 480px) {
      max-width: 90vw !important;
      min-width: 280px !important;
    }
  }

  .mat-mdc-menu-content {
    padding: 0 !important;
  }
}

// Header Section
.notification-header {
  background: #3f51b5;
  color: white;
  padding: 16px 20px;
  border-bottom: none;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
      letter-spacing: 0.25px;
      flex: 1;
    }

    .header-actions {
      display: flex;
      align-items: center;

      .clear-all-btn {
        color: white;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        transition: all 0.2s ease;
        width: 36px;
        height: 36px;
        min-width: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: scale(1.05);
        }

        mat-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
      }
    }
  }

  .notification-count {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;

    .unread-count {
      font-weight: 500;
      color: #ffeb3b;
    }
  }
}

// Notification List
.notification-list {
  max-height: 360px;
  overflow-y: auto;
  background: #ffffff;

  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f5f5;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Individual Notification Item
.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  min-height: 64px;
  gap: 0;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #f5f5f5;
  }

  &.unread {
    background: #f8f9fa;
    border-left: 3px solid #3f51b5;

    &:hover {
      background: #e8eaf6;
    }

    .notification-title {
      font-weight: 600;
      color: #1a237e;
    }
  }

  // Notification Icon
  .notification-icon {
    margin-right: 12px;
    margin-top: 0;
    flex-shrink: 0;
    display: flex;
    align-items: flex-start;
    padding-top: 2px;

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      padding: 6px;
      background: rgba(63, 81, 181, 0.1);
      transition: all 0.2s ease;
      color: #3f51b5;
      display: flex;
      align-items: center;
      justify-content: center;

      &.unread-icon {
        background: rgba(63, 81, 181, 0.15);
        color: #1a237e;
        animation: pulse 2s infinite;
      }
    }
  }

  // Notification Content
  .notification-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 1px;

    .notification-title {
      font-size: 14px;
      font-weight: 500;
      color: #212121;
      margin-bottom: 4px;
      line-height: 1.4;
      display: flex;
      align-items: center;
      gap: 6px;

      .unread-indicator {
        width: 6px;
        height: 6px;
        background: #3f51b5;
        border-radius: 50%;
        flex-shrink: 0;
        animation: pulse 2s infinite;
      }
    }

    .notification-message {
      font-size: 13px;
      color: #757575;
      line-height: 1.4;
      margin-bottom: 6px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .notification-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #9e9e9e;

      .notification-time {
        font-weight: 400;
      }

      .notification-type {
        background: rgba(63, 81, 181, 0.1);
        color: #3f51b5;
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 10px;
      }
    }
  }

  // Delete Button
  .notification-actions {
    margin-left: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
    display: flex;
    align-items: flex-start;
    padding-top: 2px;

    .delete-btn {
      width: 28px;
      height: 28px;
      line-height: 28px;
      color: #bdbdbd;
      background: transparent;
      border-radius: 50%;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: rgba(244, 67, 54, 0.1);
        color: #f44336;
      }

      mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }
  }

  &:hover .notification-actions {
    opacity: 1;
  }
}

// Show More Section
.show-more {
  padding: 12px 16px;
  text-align: center;
  background: #fafafa;
  border-top: 1px solid #e0e0e0;

  .show-more-btn {
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px;
    padding: 8px 16px;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba(63, 81, 181, 0.04);
    }

    mat-icon {
      margin-right: 4px;
      font-size: 16px;
    }
  }
}

// No Notifications State
.no-notifications {
  padding: 32px 24px;
  text-align: center;
  background: white;

  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
    color: #e0e0e0;
    opacity: 0.7;
  }

  p {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 500;
    color: #757575;
  }

  .no-notifications-subtitle {
    font-size: 14px;
    color: #9e9e9e;
    line-height: 1.4;
  }
}

// Animations
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// Global Snackbar Styles
::ng-deep {
  .notification-snackbar {
    background: #333 !important;
    color: white !important;

    .mat-mdc-snack-bar-action {
      color: #ffeb3b !important;
      font-weight: 600 !important;
    }
  }

  .success-snackbar {
    background: #4caf50 !important;
    color: white !important;
  }
}

// Responsive Design
@media (max-width: 768px) {
  ::ng-deep .notification-menu {
    .mat-mdc-menu-panel {
      max-width: 95vw !important;
      min-width: 280px !important;
    }
  }

  .notification-item {
    padding: 10px 12px;
    min-height: 60px;

    .notification-icon mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
      padding: 6px;
    }

    .notification-content {
      .notification-title {
        font-size: 13px;
      }

      .notification-message {
        font-size: 12px;
      }

      .notification-meta {
        font-size: 11px;

        .notification-type {
          font-size: 9px;
          padding: 1px 6px;
        }
      }
    }
  }

  .notification-header {
    padding: 12px 16px;

    .header-content h3 {
      font-size: 16px;
    }

    .notification-count {
      font-size: 13px;
    }
  }
}
