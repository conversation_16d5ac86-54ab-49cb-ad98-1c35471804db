/* You can add global styles to this file, and also import other style files */

// Import Angular Material styles
@import '@angular/material/prebuilt-themes/indigo-pink.css';

// Absa Brand Colors
:root {
  --absa-red: #E31837;
  --absa-dark-blue: #003366;
  --absa-light-blue: #0066CC;
  --absa-gold: #FFB81C;
  --absa-green: #00A651;
  --absa-gray-dark: #333333;
  --absa-gray-medium: #666666;
  --absa-gray-light: #F5F5F5;
  --absa-white: #FFFFFF;

  // Status colors with Absa branding
  --status-pending: #FFB81C;
  --status-approved: #0066CC;
  --status-issued: #E31837;
  --status-returned: #00A651;
  --status-completed: #00A651;
  --status-cancelled: #CC0000;

  // Alert colors
  --alert-critical: #CC0000;
  --alert-high: #E31837;
  --alert-medium: #FFB81C;
  --alert-low: #0066CC;

  // Base spacing unit
  --spacing-unit: 16px;
}

// Global styles
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Roboto', 'Helvetica Neue', sans-serif;
  color: var(--absa-gray-dark);
}

body {
  background-color: var(--absa-gray-light);
}

// Absa Brand Typography
h1, h2, h3, h4, h5, h6 {
  color: var(--absa-dark-blue);
  font-weight: 600;
}

// Custom utility classes
.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.absa-primary {
  color: var(--absa-red) !important;
}

.absa-secondary {
  color: var(--absa-dark-blue) !important;
}

.absa-accent {
  color: var(--absa-gold) !important;
}

// Override Material Design primary colors with Absa branding
.mat-toolbar.mat-primary {
  background-color: var(--absa-red) !important;
  color: var(--absa-white) !important;
}

.mat-raised-button.mat-primary {
  background-color: var(--absa-red) !important;
  color: var(--absa-white) !important;
}

.mat-flat-button.mat-primary {
  background-color: var(--absa-red) !important;
  color: var(--absa-white) !important;
}

.mat-fab.mat-primary {
  background-color: var(--absa-red) !important;
  color: var(--absa-white) !important;
}

.mat-mini-fab.mat-primary {
  background-color: var(--absa-red) !important;
  color: var(--absa-white) !important;
}

// Status chip styling with white text
.status-chip {
  color: var(--absa-white) !important;

  &.status-pending {
    background-color: var(--status-pending) !important;
  }

  &.status-approved {
    background-color: var(--status-approved) !important;
  }

  &.status-issued {
    background-color: var(--status-issued) !important;
  }

  &.status-returned {
    background-color: var(--status-returned) !important;
  }

  &.status-completed {
    background-color: var(--status-completed) !important;
  }

  &.status-cancelled {
    background-color: var(--status-cancelled) !important;
  }

  mat-icon, span {
    color: var(--absa-white) !important;
  }
}

// Card styling with Absa branding
.mat-card {
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #E0E0E0;
}

.mat-card-header {
  background-color: var(--absa-white);
  border-bottom: 2px solid var(--absa-red);
}

// Form field styling
.mat-form-field.mat-focused .mat-form-field-label {
  color: var(--absa-red) !important;
}

.mat-form-field.mat-focused .mat-form-field-ripple {
  background-color: var(--absa-red) !important;
}

// Tab styling
.mat-tab-group.mat-primary .mat-tab-label.mat-tab-label-active {
  color: var(--absa-red) !important;
}

.mat-tab-group.mat-primary .mat-ink-bar {
  background-color: var(--absa-red) !important;
}

// Responsive design helpers
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
}

// Large screen optimizations
@media (min-width: 1600px) {
  .large-screen-optimize {
    max-width: 95% !important;
    margin: 0 auto !important;
  }

  // Optimize container spacing for large screens
  .mat-card {
    margin-bottom: 1.5rem !important;
  }

  // Better table spacing on large screens
  .mat-table {
    th, td {
      padding: 1rem 1.5rem !important;
    }
  }

  // Optimize form layouts for large screens
  .mat-form-field {
    margin-bottom: 1.5rem !important;
  }
}

@media (min-width: 1200px) and (max-width: 1599px) {
  .medium-large-optimize {
    max-width: 1600px !important;
    margin: 0 auto !important;
  }
}

// Container width optimizations
.container-responsive {
  width: 100%;
  margin: 0 auto;
  padding: 0 1rem;

  @media (min-width: 1600px) {
    max-width: 95%;
    padding: 0 2rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    max-width: 1600px;
    padding: 0 1.5rem;
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    max-width: 1200px;
  }
}

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }

// Clickable logo styles
.clickable-logo {
  cursor: pointer;
  transition: opacity 0.2s ease-in-out;

  &:hover {
    opacity: 0.8;
  }

  &:active {
    opacity: 0.6;
  }
}
