<div class="issuer-dashboard-container">
  <!-- Toolbar -->
  <mat-toolbar color="primary" class="app-toolbar">
    <div class="toolbar-logo clickable-logo" (click)="navigateToDashboard()">
      <img
        src="assets/images/Absa_logo.png"
        alt="Absa Bank Logo"
        class="toolbar-absa-logo"
        loading="lazy"
        onerror="this.style.display='none'">
    </div>
    <span class="toolbar-title">Cash Management System - Issuer Dashboard</span>

    <span class="toolbar-spacer"></span>

    <!-- Refresh Button -->
    <button mat-icon-button (click)="refreshData()" title="Refresh">
      <mat-icon>refresh</mat-icon>
    </button>

    <!-- Enhanced Notifications -->
    <app-notification-panel
      [notifications]="notifications"
      [unreadCount]="unreadNotificationCount"
      [userId]="currentUser?.id || ''"
      (notificationClick)="onNotificationClick($event)"
      (markAllAsRead)="markNotificationsAsRead()">
    </app-notification-panel>

    <!-- User Menu -->
    <button mat-icon-button [matMenuTriggerFor]="userMenu">
      <mat-icon>account_circle</mat-icon>
    </button>

    <!-- Logout Button -->
    <button mat-button (click)="logout()" class="logout-button">
      <mat-icon>logout</mat-icon>
      Logout
    </button>
  </mat-toolbar>

  <!-- Main Content -->
  <div class="dashboard-content">
    <!-- Compact Welcome Header -->
    <div class="welcome-header">
      <div class="welcome-content">
        <div class="user-info">
          <h1 class="welcome-title">Welcome, {{ currentUser?.fullName }}</h1>
          <p class="user-role">Cash Issuer • {{ currentUser?.department }}</p>
        </div>
        <div class="quick-stats">
          <div class="quick-stat">
            <span class="stat-value">{{ getPendingRequestsCount() }}</span>
            <span class="stat-label">Pending</span>
          </div>
          <div class="quick-stat">
            <span class="stat-value">{{ getActiveRequestsCount() }}</span>
            <span class="stat-label">Active</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <div class="stats-grid">
      <!-- Pending Requests Card -->
      <div class="modern-stat-card pending-card clickable" (click)="navigateToTab(0)">
        <div class="card-background"></div>
        <div class="card-content">
          <div class="stat-icon">
            <mat-icon>schedule</mat-icon>
          </div>
          <div class="stat-details">
            <div class="stat-number">{{ getPendingRequestsCount() }}</div>
            <div class="stat-label">Pending Requests</div>
            <div class="stat-description">Awaiting approval</div>
          </div>
        </div>
      </div>

      <!-- Active Requests Card -->
      <div class="modern-stat-card active-card clickable" (click)="navigateToTab(1)">
        <div class="card-background"></div>
        <div class="card-content">
          <div class="stat-icon">
            <mat-icon>payments</mat-icon>
          </div>
          <div class="stat-details">
            <div class="stat-number">{{ getActiveRequestsCount() }}</div>
            <div class="stat-label">Active Requests</div>
            <div class="stat-description">In progress</div>
          </div>
        </div>
      </div>

      <!-- Total Requests Card -->
      <div class="modern-stat-card total-card clickable" (click)="navigateToTab(2)">
        <div class="card-background"></div>
        <div class="card-content">
          <div class="stat-icon">
            <mat-icon>assessment</mat-icon>
          </div>
          <div class="stat-details">
            <div class="stat-number">{{ getTotalRequestsCount() }}</div>
            <div class="stat-label">Total Requests</div>
            <div class="stat-description">All time</div>
          </div>
        </div>
      </div>

      <!-- Inventory Value Card -->
      <div class="modern-stat-card inventory-card clickable"
           (click)="onViewInventory()"
           title="Click to view detailed inventory management">
        <div class="card-background"></div>
        <div class="card-content">
          <div class="stat-icon">
            <mat-icon>account_balance_wallet</mat-icon>
          </div>
          <div class="stat-details">
            <div class="stat-number">{{ formatCurrency(totalInventoryValue) }}</div>
            <div class="stat-label">Inventory Value</div>
            <div class="stat-description">Total available</div>
          </div>
        </div>
      </div>

      <!-- Inventory Notes Card -->
      <div class="modern-stat-card notes-card clickable"
           (click)="onViewInventory()"
           title="Click to view detailed inventory management">
        <div class="card-background"></div>
        <div class="card-content">
          <div class="stat-icon">
            <mat-icon>receipt</mat-icon>
          </div>
          <div class="stat-details">
            <div class="stat-number">{{ formatInventoryNumber(totalInventoryNotes) }}</div>
            <div class="stat-label">Notes Available</div>
            <div class="stat-description">Ready to issue</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modern Requests Section -->
    <div class="requests-section">
      <div class="requests-container">
        <div class="section-header">
          <h2 class="section-title">Cash Requests Management</h2>
          <p class="section-subtitle">Review, approve, and track cash requests</p>
        </div>
        <div class="requests-content">
          <mat-tab-group #tabGroup [selectedIndex]="selectedTabIndex" (selectedIndexChange)="onTabChange($event)">
            <!-- Pending Requests Tab -->
            <mat-tab label="Pending Requests">
              <div class="tab-content">
                <div class="table-container">
                  <table mat-table [dataSource]="pendingRequests" class="requests-table">
                    <!-- Requester Column -->
                    <ng-container matColumnDef="requester">
                      <th mat-header-cell *matHeaderCellDef>Requester</th>
                      <td mat-cell *matCellDef="let request">{{ request.requesterName }}</td>
                    </ng-container>

                    <!-- Department Column -->
                    <ng-container matColumnDef="department">
                      <th mat-header-cell *matHeaderCellDef>Department</th>
                      <td mat-cell *matCellDef="let request">{{ request.department }}</td>
                    </ng-container>

                    <!-- Amount Column -->
                    <ng-container matColumnDef="amount">
                      <th mat-header-cell *matHeaderCellDef>Amount</th>
                      <td mat-cell *matCellDef="let request">{{ formatCurrency(calculateTotalAmount(request)) }}</td>
                    </ng-container>

                    <!-- Inventory Status Column -->
                    <ng-container matColumnDef="inventory">
                      <th mat-header-cell *matHeaderCellDef>Inventory Status</th>
                      <td mat-cell *matCellDef="let request">
                        <div class="inventory-status">
                          <mat-icon
                            [color]="hasInventoryErrors(request) ? 'warn' : hasInventoryWarnings(request) ? 'accent' : 'primary'"
                            [title]="hasInventoryErrors(request) ? 'Insufficient inventory' : hasInventoryWarnings(request) ? 'Low inventory warning' : 'Inventory available'">
                            {{ hasInventoryErrors(request) ? 'error' : hasInventoryWarnings(request) ? 'warning' : 'check_circle' }}
                          </mat-icon>
                          <span class="inventory-text"
                                [ngClass]="{ 'error': hasInventoryErrors(request), 'warning': hasInventoryWarnings(request), 'success': !hasInventoryErrors(request) && !hasInventoryWarnings(request) }">
                            {{ hasInventoryErrors(request) ? 'Insufficient' : hasInventoryWarnings(request) ? 'Low Stock' : 'Available' }}
                          </span>
                        </div>
                      </td>
                    </ng-container>

                    <!-- Date Requested Column -->
                    <ng-container matColumnDef="dateRequested">
                      <th mat-header-cell *matHeaderCellDef>Date Requested</th>
                      <td mat-cell *matCellDef="let request">{{ request.dateRequested | date:'short' }}</td>
                    </ng-container>

                    <!-- Status Column -->
                    <ng-container matColumnDef="status">
                      <th mat-header-cell *matHeaderCellDef>Status</th>
                      <td mat-cell *matCellDef="let request">
                        <div class="status-chip" [ngClass]="'status-' + request.status.toLowerCase()">
                          <mat-icon>{{ getStatusIcon(request.status) }}</mat-icon>
                          <span>{{ request.status | titlecase }}</span>
                        </div>
                      </td>
                    </ng-container>

                    <!-- Actions Column -->
                    <ng-container matColumnDef="actions">
                      <th mat-header-cell *matHeaderCellDef>Actions</th>
                      <td mat-cell *matCellDef="let request">
                        <button mat-raised-button color="primary" (click)="viewRequestDetails(request)">
                          <mat-icon>visibility</mat-icon>
                          Review
                        </button>
                      </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                  </table>

                  <div *ngIf="pendingRequests.length === 0" class="no-requests">
                    <mat-icon>inbox</mat-icon>
                    <p>No pending requests</p>
                  </div>
                </div>
              </div>
            </mat-tab>

            <!-- Active Requests Tab -->
            <mat-tab label="Active Requests">
              <div class="tab-content">
                <div class="table-container">
                  <table mat-table [dataSource]="activeRequests" class="requests-table">
                    <!-- Requester Column -->
                    <ng-container matColumnDef="requester">
                      <th mat-header-cell *matHeaderCellDef>Requester</th>
                      <td mat-cell *matCellDef="let request">{{ request.requesterName }}</td>
                    </ng-container>

                    <!-- Department Column -->
                    <ng-container matColumnDef="department">
                      <th mat-header-cell *matHeaderCellDef>Department</th>
                      <td mat-cell *matCellDef="let request">{{ request.department }}</td>
                    </ng-container>

                    <!-- Amount Column -->
                    <ng-container matColumnDef="amount">
                      <th mat-header-cell *matHeaderCellDef>Amount</th>
                      <td mat-cell *matCellDef="let request">{{ formatCurrency(calculateTotalAmount(request)) }}</td>
                    </ng-container>

                    <!-- Inventory Status Column -->
                    <ng-container matColumnDef="inventory">
                      <th mat-header-cell *matHeaderCellDef>Inventory Status</th>
                      <td mat-cell *matCellDef="let request">
                        <div class="inventory-status">
                          <mat-icon
                            [color]="hasInventoryErrors(request) ? 'warn' : hasInventoryWarnings(request) ? 'accent' : 'primary'"
                            [title]="hasInventoryErrors(request) ? 'Insufficient inventory' : hasInventoryWarnings(request) ? 'Low inventory warning' : 'Inventory available'">
                            {{ hasInventoryErrors(request) ? 'error' : hasInventoryWarnings(request) ? 'warning' : 'check_circle' }}
                          </mat-icon>
                          <span class="inventory-text"
                                [ngClass]="{ 'error': hasInventoryErrors(request), 'warning': hasInventoryWarnings(request), 'success': !hasInventoryErrors(request) && !hasInventoryWarnings(request) }">
                            {{ hasInventoryErrors(request) ? 'Insufficient' : hasInventoryWarnings(request) ? 'Low Stock' : 'Available' }}
                          </span>
                        </div>
                      </td>
                    </ng-container>

                    <!-- Date Requested Column -->
                    <ng-container matColumnDef="dateRequested">
                      <th mat-header-cell *matHeaderCellDef>Date Requested</th>
                      <td mat-cell *matCellDef="let request">{{ request.dateRequested | date:'short' }}</td>
                    </ng-container>

                    <!-- Status Column -->
                    <ng-container matColumnDef="status">
                      <th mat-header-cell *matHeaderCellDef>Status</th>
                      <td mat-cell *matCellDef="let request">
                        <div class="status-chip" [ngClass]="'status-' + request.status.toLowerCase()">
                          <mat-icon>{{ getStatusIcon(request.status) }}</mat-icon>
                          <span>{{ request.status | titlecase }}</span>
                        </div>
                      </td>
                    </ng-container>

                    <!-- Actions Column -->
                    <ng-container matColumnDef="actions">
                      <th mat-header-cell *matHeaderCellDef>Actions</th>
                      <td mat-cell *matCellDef="let request">
                        <button mat-raised-button color="primary" (click)="viewRequestDetails(request)">
                          <mat-icon>visibility</mat-icon>
                          Review
                        </button>
                      </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                  </table>

                  <div *ngIf="activeRequests.length === 0" class="no-requests">
                    <mat-icon>inbox</mat-icon>
                    <p>No active requests</p>
                  </div>
                </div>
              </div>
            </mat-tab>

            <!-- All Requests Tab -->
            <mat-tab label="All Requests">
              <div class="tab-content">
                <div class="table-container">
                  <table mat-table [dataSource]="allRequests" class="requests-table">
                    <!-- Requester Column -->
                    <ng-container matColumnDef="requester">
                      <th mat-header-cell *matHeaderCellDef>Requester</th>
                      <td mat-cell *matCellDef="let request">{{ request.requesterName }}</td>
                    </ng-container>

                    <!-- Department Column -->
                    <ng-container matColumnDef="department">
                      <th mat-header-cell *matHeaderCellDef>Department</th>
                      <td mat-cell *matCellDef="let request">{{ request.department }}</td>
                    </ng-container>

                    <!-- Amount Column -->
                    <ng-container matColumnDef="amount">
                      <th mat-header-cell *matHeaderCellDef>Amount</th>
                      <td mat-cell *matCellDef="let request">{{ formatCurrency(calculateTotalAmount(request)) }}</td>
                    </ng-container>

                    <!-- Inventory Status Column -->
                    <ng-container matColumnDef="inventory">
                      <th mat-header-cell *matHeaderCellDef>Inventory Status</th>
                      <td mat-cell *matCellDef="let request">
                        <div class="inventory-status">
                          <mat-icon
                            [color]="hasInventoryErrors(request) ? 'warn' : hasInventoryWarnings(request) ? 'accent' : 'primary'"
                            [title]="hasInventoryErrors(request) ? 'Insufficient inventory' : hasInventoryWarnings(request) ? 'Low inventory warning' : 'Inventory available'">
                            {{ hasInventoryErrors(request) ? 'error' : hasInventoryWarnings(request) ? 'warning' : 'check_circle' }}
                          </mat-icon>
                          <span class="inventory-text"
                                [ngClass]="{ 'error': hasInventoryErrors(request), 'warning': hasInventoryWarnings(request), 'success': !hasInventoryErrors(request) && !hasInventoryWarnings(request) }">
                            {{ hasInventoryErrors(request) ? 'Insufficient' : hasInventoryWarnings(request) ? 'Low Stock' : 'Available' }}
                          </span>
                        </div>
                      </td>
                    </ng-container>

                    <!-- Date Requested Column -->
                    <ng-container matColumnDef="dateRequested">
                      <th mat-header-cell *matHeaderCellDef>Date Requested</th>
                      <td mat-cell *matCellDef="let request">{{ request.dateRequested | date:'short' }}</td>
                    </ng-container>

                    <!-- Status Column -->
                    <ng-container matColumnDef="status">
                      <th mat-header-cell *matHeaderCellDef>Status</th>
                      <td mat-cell *matCellDef="let request">
                        <div class="status-chip" [ngClass]="'status-' + request.status.toLowerCase()">
                          <mat-icon>{{ getStatusIcon(request.status) }}</mat-icon>
                          <span>{{ request.status | titlecase }}</span>
                        </div>
                      </td>
                    </ng-container>

                    <!-- Actions Column -->
                    <ng-container matColumnDef="actions">
                      <th mat-header-cell *matHeaderCellDef>Actions</th>
                      <td mat-cell *matCellDef="let request">
                        <button mat-raised-button color="primary" (click)="viewRequestDetails(request)">
                          <mat-icon>visibility</mat-icon>
                          Review
                        </button>
                      </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                  </table>

                  <div *ngIf="allRequests.length === 0" class="no-requests">
                    <mat-icon>inbox</mat-icon>
                    <p>No requests found</p>
                  </div>
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>
        </div>
      </div>
    </div>
  </div>
</div>



<!-- User Menu -->
<mat-menu #userMenu="matMenu">
  <button mat-menu-item>
    <mat-icon>person</mat-icon>
    <span>{{ currentUser?.fullName }}</span>
  </button>
  <button mat-menu-item>
    <mat-icon>business</mat-icon>
    <span>{{ currentUser?.department }}</span>
  </button>
  <mat-divider></mat-divider>
  <button mat-menu-item (click)="onViewInventory()">
    <mat-icon>inventory</mat-icon>
    <span>Inventory Management</span>
  </button>
  <mat-divider></mat-divider>
  <button mat-menu-item (click)="logout()">
    <mat-icon>logout</mat-icon>
    <span>Logout</span>
  </button>
</mat-menu>
