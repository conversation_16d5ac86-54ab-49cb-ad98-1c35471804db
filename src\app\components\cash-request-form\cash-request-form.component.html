<div class="cash-request-wizard">
  <!-- Standardized Navigation Toolbar -->
  <mat-toolbar color="primary" class="app-toolbar">
    <button mat-icon-button (click)="goBack()" class="back-button" title="Go Back">
      <mat-icon>arrow_back</mat-icon>
    </button>

    <div class="toolbar-logo clickable-logo" (click)="navigateToDashboard()">
      <img
        src="assets/images/Absa_logo.png"
        alt="Absa Bank Logo"
        class="toolbar-absa-logo"
        loading="lazy"
        onerror="this.style.display='none'">
    </div>

    <span class="toolbar-title">Cash Management System - New Request</span>

    <span class="toolbar-spacer"></span>

    <button mat-icon-button [matMenuTriggerFor]="userMenu" class="user-menu-button" title="User Menu">
      <mat-icon>account_circle</mat-icon>
    </button>
  </mat-toolbar>

  <!-- Progress Steps Section -->
  <div class="progress-section">
    <div class="progress-container">
      <div class="progress-steps">
        <div class="progress-step" [class.active]="currentStep === 1" [class.completed]="currentStep > 1" (click)="goToStep(1)">
          <div class="step-circle">
            <span class="step-number" *ngIf="currentStep <= 1">1</span>
            <mat-icon *ngIf="currentStep > 1" class="step-check">check</mat-icon>
          </div>
          <span class="step-label">Details</span>
        </div>

        <div class="progress-line" [class.completed]="currentStep > 1"></div>

        <div class="progress-step" [class.active]="currentStep === 2" [class.completed]="currentStep > 2" (click)="goToStep(2)">
          <div class="step-circle">
            <span class="step-number" *ngIf="currentStep <= 2">2</span>
            <mat-icon *ngIf="currentStep > 2" class="step-check">check</mat-icon>
          </div>
          <span class="step-label">Selection</span>
        </div>

        <div class="progress-line" [class.completed]="currentStep > 2"></div>

        <div class="progress-step" [class.active]="currentStep === 3" [class.completed]="currentStep > 3" (click)="goToStep(3)">
          <div class="step-circle">
            <span class="step-number" *ngIf="currentStep <= 3">3</span>
            <mat-icon *ngIf="currentStep > 3" class="step-check">check</mat-icon>
          </div>
          <span class="step-label">Review</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Wizard Content -->
  <div class="wizard-content">
    <form (ngSubmit)="onSubmit()" #requestForm="ngForm">

      <!-- Step 1: Requester Details -->
      <div class="wizard-step" *ngIf="currentStep === 1" [@slideIn]>
        <div class="step-container">
          <div class="step-header">
            <div class="step-info">
              <h2 class="step-title">Requester Information</h2>
              <p class="step-description">Please verify your details before proceeding</p>
            </div>
          </div>

          <div class="step-content">
            <div class="info-cards">
              <div class="info-card requester-card">
                <div class="card-header">
                  <mat-icon class="card-icon">badge</mat-icon>
                  <h3>Personal Details</h3>
                </div>
                <div class="card-content">
                  <mat-form-field appearance="fill" class="modern-field">
                    <mat-label>Requester Name</mat-label>
                    <input matInput [value]="currentUser?.fullName" readonly>
                    <mat-icon matSuffix>person</mat-icon>
                  </mat-form-field>
                </div>
              </div>

              <div class="info-card department-card">
                <div class="card-header">
                  <mat-icon class="card-icon">business</mat-icon>
                  <h3>Department</h3>
                </div>
                <div class="card-content">
                  <mat-form-field appearance="fill" class="modern-field">
                    <mat-label>Select Department</mat-label>
                    <mat-select [(ngModel)]="selectedDepartment" name="department" required>
                      <mat-option value="APC">
                        <mat-icon>bug_report</mat-icon>
                        APC
                      </mat-option>
                      <mat-option value="FFA">
                        <mat-icon>devices</mat-icon>
                        FFA
                      </mat-option>
                      <mat-option value="NCR">
                        <mat-icon>gavel</mat-icon>
                        NCR
                      </mat-option>
                    </mat-select>
                    <mat-icon matSuffix>business</mat-icon>
                  </mat-form-field>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Cash Selection -->
      <div class="wizard-step" *ngIf="currentStep === 2" [@slideIn]>
        <div class="step-container">
          <div class="step-header">
            <div class="step-info">
              <h2 class="step-title">Cash Selection</h2>
              <p class="step-description">Choose your required denominations and quantities</p>
            </div>
          </div>

          <div class="step-content">
            <!-- Smart Mode Toggle -->
            <div class="smart-mode-panel">
              <div class="smart-toggle-container">
                <mat-slide-toggle
                  [checked]="smartMode"
                  (change)="onSmartModeToggle($event.checked)"
                  color="primary"
                  class="smart-toggle">
                  <span class="toggle-content">
                    <mat-icon class="toggle-icon">auto_awesome</mat-icon>
                    <span class="toggle-text">Smart Series Selection</span>
                  </span>
                </mat-slide-toggle>
                <div class="smart-description"
                     *ngIf="smartMode"
                     [@fadeInOut]>
                  <mat-icon class="info-icon">lightbulb</mat-icon>
                  <span>AI-powered recommendations for optimal note series selection</span>
                </div>
              </div>
            </div>

            <!-- Availability Warnings -->
            <div class="warning-panel" *ngIf="hasAvailabilityWarnings()">
              <div class="warning-content">
                <div class="warning-header">
                  <mat-icon class="warning-icon">warning</mat-icon>
                  <h4>Availability Alerts</h4>
                </div>
                <div class="warning-list">
                  <div *ngFor="let warning of getAvailabilityWarnings()" class="warning-item">
                    <mat-icon class="item-icon">info</mat-icon>
                    <span>{{ warning }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Denomination Selection Grid -->
            <div class="denominations-container">
              <h3 class="section-title">
                <mat-icon>account_balance_wallet</mat-icon>
                Bank Note Selection
              </h3>

              <div class="denominations-grid">
                <div *ngFor="let denom of denominations" class="denomination-card">
                  <div class="card-header">
                    <div class="denomination-image-container">
                      <img
                        [src]="'assets/images/Money/' + denom.label + '.jpg'"
                        [alt]="denom.label + ' South African Rand Note'"
                        class="denomination-image"
                        loading="lazy"
                        onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                      <div class="image-fallback" style="display: none;">
                        <mat-icon class="fallback-icon">payments</mat-icon>
                      </div>
                    </div>
                    <div class="denomination-info">
                      <div class="denomination-value">{{ denom.label }}</div>
                      <div class="denomination-currency">South African Rand</div>
                    </div>
                    <div class="status-indicator">
                      <mat-chip
                        [class]="getInventoryStatusClass(getInventoryStatus(denom.value))"
                        class="status-chip">
                        <mat-icon class="status-icon">{{ getInventoryStatus(denom.value) === InventoryStatus.AVAILABLE ? 'check_circle' :
                                     getInventoryStatus(denom.value) === InventoryStatus.LOW_STOCK ? 'warning' : 'error' }}</mat-icon>
                        <span>{{ getInventoryStatusLabel(getInventoryStatus(denom.value)) }}</span>
                      </mat-chip>
                    </div>
                  </div>

                  <div class="card-content">
                    <!-- Series Selection (Smart Mode) -->
                    <div class="series-selection-container" *ngIf="smartMode">
                      <mat-form-field appearance="fill" class="series-select-field">
                        <mat-label>
                          <mat-icon class="series-icon">category</mat-icon>
                          Money Series
                        </mat-label>
                        <mat-select
                          [(ngModel)]="selectedSeries[denom.value]"
                          (selectionChange)="onSeriesSelectChange(denom.value, $event.value)"
                          [name]="'series-' + denom.value">

                          <!-- Custom trigger to show only clean series name when closed -->
                          <mat-select-trigger>
                            {{ selectedSeries[denom.value] ? getSeriesLabel(selectedSeries[denom.value]!) : 'Select Series' }}
                          </mat-select-trigger>

                          <!-- Options with full details when dropdown is open -->
                          <mat-option
                            *ngFor="let seriesOption of getSeriesOptions(denom.value); trackBy: trackBySeries"
                            [value]="seriesOption.series"
                            [class]="getSeriesStatusClass(seriesOption)">

                            <div class="option-layout">
                              <span class="series-name">{{ getSeriesLabel(seriesOption.series) }}</span>

                              <div class="option-details">
                                <span class="series-badges" *ngIf="isSeriesRecommended(seriesOption)">
                                  <mat-icon class="recommended-icon">star</mat-icon>
                                  <span class="recommended-text">Recommended</span>
                                </span>

                                <span class="series-availability">
                                  <mat-icon class="status-icon" [ngClass]="getSeriesStatusClass(seriesOption)">
                                    {{ getSeriesStatusIcon(seriesOption) }}
                                  </mat-icon>
                                  <span class="availability-count">{{ seriesOption.available }} available</span>
                                </span>
                              </div>
                            </div>
                          </mat-option>
                        </mat-select>
                      </mat-form-field>
                    </div>

                    <!-- Quantity Input -->
                    <div class="quantity-section">
                      <mat-form-field appearance="fill" class="quantity-field">
                        <mat-label>Quantity Required</mat-label>
                        <input
                          matInput
                          type="number"
                          min="0"
                          [value]="getQuantity(denom.value)"
                          (input)="updateQuantity(denom.value, +$any($event.target).value)"
                          [disabled]="getInventoryStatus(denom.value) === InventoryStatus.OUT_OF_STOCK"
                          placeholder="0">
                        <mat-icon matSuffix>format_list_numbered</mat-icon>
                      </mat-form-field>
                      <div class="amount-display">
                        <span class="amount-label">Total:</span>
                        <span class="amount-value">{{ formatCurrency(denom.value * getQuantity(denom.value)) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Additional Options -->
            <div class="options-container">
              <h3 class="section-title">
                <mat-icon>tune</mat-icon>
                Additional Options
              </h3>

              <div class="options-grid">
                <!-- Coins Option -->
                <div class="option-card coins-card">
                  <div class="option-header">
                    <div class="option-image-container">
                      <img
                        src="assets/images/Money/Coin.png"
                        alt="South African Coins"
                        class="option-image"
                        loading="lazy"
                        onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                      <div class="image-fallback" style="display: none;">
                        <div class="rand-symbol">R</div>
                      </div>
                    </div>
                    <div class="option-info">
                      <h4>Coins Request</h4>
                      <p>Include South African Rand coins</p>
                    </div>
                    <div class="option-toggle-container">
                      <mat-slide-toggle
                        [(ngModel)]="coinsRequested"
                        name="coinsRequested"
                        (change)="onCoinsToggleChange($event.checked)"
                        color="primary"
                        class="option-toggle" style="color: White;">
                        Request Coins
                      </mat-slide-toggle>
                    </div>
                  </div>
                  <div class="option-content">
                    <div class="option-confirmation" *ngIf="coinsRequested">
                      <mat-icon class="confirm-icon">check_circle</mat-icon>
                      <span>Coins will be included</span>
                    </div>
                  </div>
                </div>

                <!-- Dye Pack Option -->
                <div class="option-card dye-pack-card">
                  <div class="option-header">
                    <div class="option-image-container">
                      <img
                        src="assets/images/Money/Dye Stained.jpg"
                        alt="Security Dye Pack"
                        class="option-image"
                        loading="lazy"
                        onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                      <div class="image-fallback" style="display: none;">
                        <mat-icon class="fallback-icon security-icon">security</mat-icon>
                      </div>
                    </div>
                    <div class="option-info">
                      <h4>Security Dye Pack</h4>
                      <p>Enhanced security protection for cash transport</p>
                    </div>
                    <div class="option-toggle-container">
                      <mat-slide-toggle
                        [(ngModel)]="dyePackRequired"
                        name="dyePackRequired"
                        (change)="onDyePackToggleChange($event.checked)"
                        color="primary"
                        class="option-toggle">
                        Require Dye Pack
                      </mat-slide-toggle>
                    </div>
                  </div>
                  <div class="option-content">
                    <div class="option-confirmation" *ngIf="dyePackRequired">
                      <mat-icon class="confirm-icon">check_circle</mat-icon>
                      <span>Security dye pack will be included</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Running Total -->
            <div class="running-total">
              <div class="total-container">
                <div class="total-main">
                  <span class="total-label">Total Amount</span>
                  <span class="total-amount">{{ formatCurrency(calculateTotal()) }}</span>
                </div>
                <div class="total-extras" *ngIf="coinsRequested || dyePackRequired">
                  <div class="extra-item" *ngIf="coinsRequested">
                    <div class="extra-icon-container">
                      <span class="rand-symbol-small">R</span>
                    </div>
                    <span>+ Coins</span>
                  </div>
                  <div class="extra-item" *ngIf="dyePackRequired">
                    <mat-icon class="security-icon">security</mat-icon>
                    <span>+ Dye Pack</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Review & Submit -->
      <div class="wizard-step" *ngIf="currentStep === 3" [@slideIn]>
        <div class="step-container">
          <div class="step-header">
            <div class="step-info">
              <h2 class="step-title">Review & Submit</h2>
              <p class="step-description">Verify your request details before submission</p>
            </div>
          </div>

          <div class="step-content">
            <!-- Requester Summary -->
            <div class="requester-summary">
              <h3 class="section-title">
                <mat-icon>person</mat-icon>
                Requester Information
              </h3>

              <div class="summary-card">
                <div class="summary-row">
                  <span class="label">Name:</span>
                  <span class="value">{{ currentUser?.fullName }}</span>
                </div>
                <div class="summary-row">
                  <span class="label">Department:</span>
                  <span class="value">{{ selectedDepartment }}</span>
                </div>
                <div class="summary-row">
                  <span class="label">Request Date:</span>
                  <span class="value">{{ dateRequested | date:'medium' }}</span>
                </div>
              </div>
            </div>

            <!-- Request Details -->
            <div class="details-container">
              <h3 class="section-title">
                <mat-icon>description</mat-icon>
                Additional Details
              </h3>

              <div class="details-card">
                <div class="details-content">
                  <div class="detail-field">
                    <div class="field-header">
                      <mat-icon class="field-icon">event</mat-icon>
                      <span class="field-label">Date and Time Requested</span>
                    </div>
                    <mat-form-field appearance="outline" class="compact-field">
                      <input matInput [matDatepicker]="picker" [(ngModel)]="dateRequested" name="dateRequested" required>
                      <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                      <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>
                  </div>

                  <div class="detail-field">
                    <div class="field-header">
                      <mat-icon class="field-icon">comment</mat-icon>
                      <span class="field-label">Comments <span class="optional-text">(Optional)</span></span>
                    </div>
                    <mat-form-field appearance="outline" class="compact-field">
                      <textarea
                        matInput
                        [(ngModel)]="comments"
                        name="comments"
                        rows="2"
                        placeholder="Any additional information or special requirements...">
                      </textarea>
                    </mat-form-field>
                  </div>
                </div>
              </div>
            </div>

            <!-- Final Summary -->
            <div class="final-summary" *ngIf="getSelectedNotes().length > 0 || coinsRequested || dyePackRequired">
              <h3 class="section-title">
                <mat-icon>receipt_long</mat-icon>
                Request Summary
              </h3>

              <div class="summary-container">
                <!-- Items Section -->
                <div class="summary-section">
                  <div class="section-header">
                    <mat-icon class="section-icon">inventory_2</mat-icon>
                    <span class="section-label">Selected Items</span>
                  </div>

                  <div class="items-list">
                    <div class="summary-item" *ngFor="let note of getSelectedNotes()">
                      <div class="item-content">
                        <div class="item-icon-wrapper">
                          <mat-icon class="item-icon banknote-icon">payments</mat-icon>
                        </div>
                        <div class="item-details">
                          <span class="item-name">{{ note.denomination | currency:'ZAR':'symbol':'1.0-0' }} Bank Notes</span>
                          <span class="item-quantity">Quantity: {{ note.quantity }}</span>
                        </div>
                        <div class="item-amount">{{ formatCurrency(note.denomination * note.quantity) }}</div>
                      </div>
                    </div>

                    <div class="summary-item" *ngIf="coinsRequested">
                      <div class="item-content">
                        <div class="item-icon-wrapper coins-wrapper">
                          <span class="rand-symbol">R</span>
                        </div>
                        <div class="item-details">
                          <span class="item-name">South African Rand Coins</span>
                          <span class="item-quantity">As requested</span>
                        </div>
                        <div class="item-status">
                          <mat-icon class="status-icon">check_circle</mat-icon>
                        </div>
                      </div>
                    </div>

                    <div class="summary-item" *ngIf="dyePackRequired">
                      <div class="item-content">
                        <div class="item-icon-wrapper security-wrapper">
                          <mat-icon class="item-icon security-icon">security</mat-icon>
                        </div>
                        <div class="item-details">
                          <span class="item-name">Security Dye Pack</span>
                          <span class="item-quantity">Enhanced protection</span>
                        </div>
                        <div class="item-status">
                          <mat-icon class="status-icon">check_circle</mat-icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Total Section -->
                <div class="total-section" *ngIf="calculateTotal() > 0">
                  <div class="total-content">
                    <div class="total-info">
                      <mat-icon class="total-icon">account_balance_wallet</mat-icon>
                      <span class="total-label">Total Cash Amount</span>
                    </div>
                    <div class="total-amount">{{ formatCurrency(calculateTotal()) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </form>
  </div>

  <!-- Footer Navigation -->
  <footer class="wizard-footer">
    <div class="footer-content">
      <div class="footer-left">
        <button
          mat-button
          (click)="previousStep()"
          *ngIf="currentStep > 1"
          class="footer-btn prev-btn">
          <mat-icon>arrow_back</mat-icon>
          Previous
        </button>
      </div>

      <div class="footer-center">
        <div class="step-progress">
          <span class="step-indicator">Step {{ currentStep }} of 3</span>
          <div class="progress-bar">
            <div class="progress-fill" [style.width.%]="(currentStep / 3) * 100"></div>
          </div>
        </div>
      </div>

      <div class="footer-right">
        <button
          mat-button
          (click)="onCancel()"
          class="footer-btn cancel-btn">
          <mat-icon>close</mat-icon>
          Cancel
        </button>

        <button
          mat-raised-button
          color="primary"
          (click)="nextStep()"
          *ngIf="currentStep < 3"
          [disabled]="!isStepValid(currentStep)"
          class="footer-btn next-btn">
          Next
          <mat-icon>arrow_forward</mat-icon>
        </button>

        <button
          mat-raised-button
          color="primary"
          (click)="onSubmit()"
          *ngIf="currentStep === 3"
          [disabled]="!isFormValid()"
          class="footer-btn submit-btn">
          <mat-icon>send</mat-icon>
          Submit Request
        </button>
      </div>
    </div>
  </footer>
</div>

<!-- User Menu -->
<mat-menu #userMenu="matMenu">
  <button mat-menu-item>
    <mat-icon>person</mat-icon>
    <span>{{ currentUser?.fullName }}</span>
  </button>
  <button mat-menu-item>
    <mat-icon>business</mat-icon>
    <span>{{ currentUser?.department }}</span>
  </button>
  <mat-divider></mat-divider>
  <button mat-menu-item (click)="goBack()">
    <mat-icon>dashboard</mat-icon>
    <span>Dashboard</span>
  </button>
</mat-menu>
