.issuer-dashboard-container {
  min-height: calc(100vh - 64px); // Subtract header height
  background-color: #f5f5f5;
}

.app-toolbar {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  padding: 0 1rem;

  .toolbar-logo {
    display: flex;
    align-items: center;
    margin-right: 1rem;

    .toolbar-absa-logo {
      height: 32px;
      width: auto;
      max-width: 120px;
      object-fit: contain;
      filter: brightness(0) invert(1); // Make logo white on red background
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        filter: brightness(0) invert(1) drop-shadow(0 2px 4px rgba(255,255,255,0.3));
      }

      @media (max-width: 768px) {
        height: 28px;
        max-width: 100px;
      }

      @media (max-width: 480px) {
        height: 24px;
        max-width: 80px;
      }
    }
  }

  .toolbar-title {
    margin-left: 16px;
    font-size: 1.2rem;
    font-weight: 500;
  }

  .toolbar-spacer {
    flex: 1 1 auto;
  }

  .logout-button {
    margin-left: 8px;
    color: white;

    mat-icon {
      margin-right: 4px;
    }
  }
}

// Enhanced accessibility
@media (prefers-reduced-motion: reduce) {
  .toolbar-absa-logo {
    animation: none !important;
    transition: none !important;

    &:hover {
      transform: none !important;
    }
  }
}

.dashboard-content {
  padding: 1rem 2rem 0.5rem 2rem; // Reduced bottom padding
  max-width: 1800px;
  margin: 0 auto;
  width: 100%;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: calc(100vh - 64px);

  // Large screens - utilize more space
  @media (min-width: 1600px) {
    max-width: 95%;
    padding: 1rem 3rem 0.5rem 3rem; // Reduced bottom padding
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    max-width: 1600px;
    padding: 1rem 2.5rem 0.5rem 2.5rem; // Reduced bottom padding
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    max-width: 1200px;
    padding: 1rem 2rem 0.5rem 2rem; // Reduced bottom padding
  }

  @media (max-width: 768px) {
    padding: 1rem 1rem 0.5rem 1rem; // Reduced bottom padding
  }
}

// Modern Welcome Header
.welcome-header {
  background: linear-gradient(135deg, var(--absa-red) 0%, #d32f2f 100%);
  border-radius: 16px;
  padding: 1.5rem 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 8px 32px rgba(206,14,45,0.2);
  color: white;

  .welcome-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
  }

  .user-info {
    .welcome-title {
      font-size: 1.75rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      color: white;
      letter-spacing: 0.5px;

      @media (max-width: 768px) {
        font-size: 1.5rem;
      }
    }

    .user-role {
      font-size: 1rem;
      opacity: 0.9;
      margin: 0;
      font-weight: 500;
    }
  }

  .quick-stats {
    display: flex;
    gap: 2rem;

    @media (max-width: 768px) {
      gap: 1.5rem;
    }

    .quick-stat {
      text-align: center;

      .stat-value {
        display: block;
        font-size: 2rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.25rem;

        @media (max-width: 768px) {
          font-size: 1.5rem;
        }
      }

      .stat-label {
        font-size: 0.875rem;
        opacity: 0.9;
        font-weight: 500;
      }
    }
  }
}

// Modern Statistics Grid
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  // Large screens - optimize for 5 columns
  @media (min-width: 1600px) {
    grid-template-columns: repeat(5, 1fr);
    gap: 2rem;
  }

  @media (min-width: 1200px) and (max-width: 1599px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.75rem;
  }

  @media (min-width: 900px) and (max-width: 1199px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

// Modern Stat Cards
.modern-stat-card {
  position: relative;
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  border: 1px solid rgba(0,0,0,0.05);

  &.clickable {
    cursor: pointer;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
  }

  .card-background {
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    opacity: 0.1;
    transform: translate(30px, -30px);
  }

  .card-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    mat-icon {
      font-size: 1.75rem;
      width: 1.75rem;
      height: 1.75rem;
      color: white;
    }
  }

  .stat-details {
    flex: 1;

    .stat-number {
      font-size: 1.75rem;
      font-weight: 700;
      line-height: 1;
      margin-bottom: 0.25rem;
      color: var(--absa-dark-blue);
    }

    .stat-label {
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--absa-dark-blue);
      margin-bottom: 0.25rem;
    }

    .stat-description {
      font-size: 0.8rem;
      color: var(--absa-gray-medium);
      font-weight: 500;
    }
  }

  // Card-specific styling
  &.pending-card {
    .card-background {
      background: var(--absa-gold);
    }
    .stat-icon {
      background: linear-gradient(135deg, var(--absa-gold) 0%, #f57c00 100%);
    }
  }

  &.active-card {
    .card-background {
      background: var(--absa-light-blue);
    }
    .stat-icon {
      background: linear-gradient(135deg, var(--absa-light-blue) 0%, #1976d2 100%);
    }
  }

  &.total-card {
    .card-background {
      background: var(--absa-green);
    }
    .stat-icon {
      background: linear-gradient(135deg, var(--absa-green) 0%, #388e3c 100%);
    }
  }

  &.inventory-card {
    .card-background {
      background: var(--absa-green);
    }
    .stat-icon {
      background: linear-gradient(135deg, var(--absa-green) 0%, #388e3c 100%);
    }
  }

  &.notes-card {
    .card-background {
      background: var(--absa-gold);
    }
    .stat-icon {
      background: linear-gradient(135deg, var(--absa-gold) 0%, #f57c00 100%);
    }
  }
}

// Legacy stat-card styles (keeping for backward compatibility)
.stat-card {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  border-radius: 12px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &.clickable {
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(0,0,0,0.15);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
  }
}

// Actions Section
.actions-section {
  margin-bottom: 2rem;

  .actions-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);

    mat-card-header {
      background-color: var(--absa-white);
      border-bottom: 2px solid var(--absa-red);

      mat-card-title {
        color: var(--absa-dark-blue);
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-top: 1rem;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }

      .action-button {
        padding: 1rem;
        height: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        border-radius: 8px;
        font-weight: 500;

        mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }
  }
}

// Modern Requests Section
.requests-section {
  .requests-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    border: 1px solid rgba(0,0,0,0.05);
  }

  .section-header {
    background: linear-gradient(135deg, var(--absa-dark-blue) 0%, var(--absa-light-blue) 100%);
    color: white;
    padding: 1.5rem 2rem;

    .section-title {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      letter-spacing: 0.5px;
      color: white !important;
    }

    .section-subtitle {
      font-size: 1rem;
      margin: 0;
      opacity: 0.9;
      font-weight: 500;
      color: white !important;
    }
  }

  .requests-content {
    padding: 0;

    ::ng-deep .mat-mdc-tab-group {
      .mat-mdc-tab-header {
        border-bottom: 1px solid rgba(0,0,0,0.1);
        background: #f8f9fa;

        .mat-mdc-tab {
          min-width: 160px;
          font-weight: 600;
          color: var(--absa-dark-blue);

          &.mdc-tab--active {
            color: var(--absa-red);
          }
        }

        .mat-ink-bar {
          background-color: var(--absa-red);
          height: 3px;
        }
      }

      .mat-mdc-tab-body-wrapper {
        .mat-mdc-tab-body {
          .mat-mdc-tab-body-content {
            padding: 0;
          }
        }
      }
    }
  }
}

.tab-content {
  padding: 2rem;
  background: white;
}

.table-container {
  overflow-x: auto;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  border: 1px solid rgba(0,0,0,0.05);
}

.requests-table {
  width: 100%;
  background: white;

  th {
    font-weight: 700;
    color: var(--absa-dark-blue);
    background: #f8f9fa;
    padding: 1.25rem 1rem;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    border-bottom: 2px solid rgba(0,0,0,0.1);
  }

  td {
    padding: 1.25rem 1rem;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    font-size: 0.9rem;
    color: var(--absa-dark-blue);
  }

  tr {
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(0,102,204,0.02);
    }

    &:last-child td {
      border-bottom: none;
    }
  }

  .status-chip {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    color: white !important;

    mat-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
      color: white !important;
    }

    span {
      line-height: 1;
      color: white !important;
    }

    &.status-pending {
      background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
      border: 1px solid #ff9800;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-approved {
      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
      border: 1px solid #2196f3;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-issued {
      background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
      border: 1px solid #9c27b0;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-returned {
      background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
      border: 1px solid #00bcd4;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-completed {
      background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
      border: 1px solid #4caf50;

      mat-icon, span {
        color: white !important;
      }
    }

    &.status-cancelled {
      background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
      border: 1px solid #f44336;

      mat-icon, span {
        color: white !important;
      }
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    }
  }

  mat-chip {
    mat-icon {
      margin-right: 0.25rem;
      font-size: 1rem;
    }
  }

  button {
    mat-icon {
      margin-right: 0.25rem;
    }
  }
}

// Enhanced No Requests State
.no-requests {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--absa-gray-medium);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin: 1rem;

  mat-icon {
    font-size: 4rem;
    width: 4rem;
    height: 4rem;
    margin-bottom: 1.5rem;
    color: var(--absa-gray-light);
    opacity: 0.6;
  }

  p {
    font-size: 1.2rem;
    margin: 0;
    font-weight: 500;
    color: var(--absa-gray-medium);
  }
}

// Enhanced Action Buttons
.requests-table {
  button {
    &[mat-raised-button] {
      border-radius: 8px;
      font-weight: 600;
      padding: 0.5rem 1.25rem;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      transition: all 0.2s ease;
      text-transform: none;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      }

      mat-icon {
        margin-right: 0.5rem;
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }
}



// Tab customizations
::ng-deep .mat-mdc-tab-group {
  .mat-mdc-tab-header {
    border-bottom: 1px solid #e0e0e0;
  }

  .mat-mdc-tab-body-content {
    padding: 0;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .requests-table {
    font-size: 0.8rem;

    th, td {
      padding: 0.5rem 0.25rem;
    }
  }

  .stat-card .stat-content {
    flex-direction: column;
    text-align: center;

    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
    }

    .stat-info .stat-number {
      font-size: 1.5rem;
    }
  }
}

// Inventory Status Styles
.inventory-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  mat-icon {
    font-size: 1.2rem;
    width: 1.2rem;
    height: 1.2rem;
  }

  .inventory-text {
    font-size: 0.875rem;
    font-weight: 500;

    &.success {
      color: #4caf50;
    }

    &.warning {
      color: #ff9800;
    }

    &.error {
      color: #f44336;
    }
  }
}

// Inventory Card Styles
.stat-card {
  &.inventory-value-card {
    border-left: 4px solid #4caf50;

    .stat-icon mat-icon {
      color: #4caf50;
    }
  }

  &.inventory-notes-card {
    border-left: 4px solid #ff9800;

    .stat-icon mat-icon {
      color: #ff9800;
    }
  }

  // Enhanced clickable styles
  &.clickable {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    }

    .stat-action {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      margin-top: 0.5rem;
      font-size: 0.875rem;
      color: var(--absa-light-blue);
      opacity: 0.8;
      transition: opacity 0.2s ease;

      .action-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }

    &:hover .stat-action {
      opacity: 1;
    }
  }
}

// Enhanced Inventory Button Styles
.toolbar-inventory-btn {
  margin-right: 1rem;
  border-radius: 20px;
  font-weight: 600;
  text-transform: none;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  transition: all 0.3s ease;

  .btn-text {
    margin-left: 0.5rem;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  }

  @media (max-width: 768px) {
    .btn-text {
      display: none;
    }
  }
}

// Enhanced Action Button Styles
.enhanced-inventory-btn {
  width: 100%;
  min-height: 80px;
  border-radius: 12px;
  text-transform: none;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: all 0.3s ease;

  .button-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0.5rem;

    .action-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      color: var(--absa-white);
    }

    .button-text {
      flex: 1;
      text-align: left;
      margin-left: 1rem;

      .primary-text {
        display: block;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--absa-white);
      }

      .secondary-text {
        display: block;
        font-size: 0.875rem;
        opacity: 0.9;
        color: var(--absa-white);
        margin-top: 0.25rem;
      }
    }

    .arrow-icon {
      color: var(--absa-white);
      opacity: 0.8;
      transition: all 0.3s ease;
    }
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.15);

    .arrow-icon {
      opacity: 1;
      transform: translateX(4px);
    }
  }

  @media (max-width: 768px) {
    min-height: 60px;

    .button-content {
      .action-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }

      .button-text {
        .primary-text {
          font-size: 1rem;
        }

        .secondary-text {
          font-size: 0.75rem;
        }
      }
    }
  }
}
