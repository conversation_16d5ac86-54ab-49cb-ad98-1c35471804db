// Absa Brand Colors
:root {
  --absa-red: #E31837;
  --absa-dark-blue: #003366;
  --absa-light-blue: #0066CC;
  --absa-gold: #FFB81C;
  --absa-green: #00A651;
  --absa-gray-light: #F5F5F5;
  --absa-gray-medium: #999999;
  --absa-gray-dark: #666666;
}

// Toolbar
.app-toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  padding: 0 1rem;

  .toolbar-logo {
    display: flex;
    align-items: center;
    margin-right: 1rem;
    margin-left: 0.5rem;

    .toolbar-absa-logo {
      height: 32px;
      width: auto;
      max-width: 120px;
      object-fit: contain;
      filter: brightness(0) invert(1); // Make logo white on red background
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        filter: brightness(0) invert(1) drop-shadow(0 2px 4px rgba(255,255,255,0.3));
      }

      @media (max-width: 768px) {
        height: 28px;
        max-width: 100px;
      }

      @media (max-width: 480px) {
        height: 24px;
        max-width: 80px;
      }
    }
  }

  .toolbar-title {
    font-size: 1.2rem;
    font-weight: 600;
  }

  .toolbar-spacer {
    flex: 1 1 auto;
  }
}

// Enhanced accessibility
@media (prefers-reduced-motion: reduce) {
  .toolbar-absa-logo {
    animation: none !important;
    transition: none !important;

    &:hover {
      transform: none !important;
    }
  }
}

// User Menu
.user-info {
  padding: 1rem;
  text-align: center;
  background-color: var(--absa-gray-light);

  .user-name {
    font-weight: 600;
    color: var(--absa-dark-blue);
    margin-bottom: 0.25rem;
  }

  .user-role {
    font-size: 0.9rem;
    color: var(--absa-gray-medium);
  }
}

// Main Content
.alerts-content {
  padding: 1.5rem;
  background-color: var(--absa-gray-light);
  min-height: calc(100vh - 64px);

  @media (max-width: 599px) {
    padding: 1rem;
  }
}

// Header Section
.header-section {
  margin-bottom: 2rem;

  .header-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 2rem;

      @media (max-width: 768px) {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
      }

      .header-info {
        h1 {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          margin: 0 0 0.5rem 0;
          color: var(--absa-dark-blue);
          font-size: 1.8rem;
          font-weight: 700;

          @media (max-width: 599px) {
            font-size: 1.5rem;
            justify-content: center;
          }

          .header-icon {
            color: var(--absa-red);
            font-size: 2rem;
          }
        }

        p {
          margin: 0;
          color: var(--absa-gray-dark);
          font-size: 1rem;
        }
      }

      .header-actions {
        button {
          padding: 0.75rem 1.5rem;
          font-weight: 600;
        }
      }
    }
  }
}

// Summary Section
.summary-section {
  margin-bottom: 2rem;

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;

    @media (min-width: 1200px) {
      grid-template-columns: repeat(4, 1fr);
      max-width: 1200px;
      margin: 0 auto;
    }

    @media (max-width: 599px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
    }
  }

  .summary-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    height: 100px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0,0,0,0.15);
    }

    &.critical {
      border-left: 4px solid #CC0000;
    }

    &.high {
      border-left: 4px solid var(--absa-red);
    }

    &.medium {
      border-left: 4px solid var(--absa-gold);
    }

    &.low {
      border-left: 4px solid var(--absa-light-blue);
    }

    mat-card-content {
      height: 100%;
      padding: 0.75rem !important;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .summary-content {
      display: flex;
      align-items: center;
      gap: 1rem;
      width: 100%;

      .summary-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: rgba(227, 24, 55, 0.1);

        mat-icon {
          font-size: 24px;
          color: var(--absa-red);
        }

        @media (max-width: 599px) {
          width: 40px;
          height: 40px;

          mat-icon {
            font-size: 20px;
          }
        }
      }

      .summary-details {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 1;

        .summary-value {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--absa-dark-blue);
          line-height: 1.2;

          @media (max-width: 599px) {
            font-size: 1.3rem;
          }
        }

        .summary-label {
          font-size: 0.85rem;
          color: var(--absa-gray-medium);
          font-weight: 500;
          margin-top: 0.25rem;
        }
      }
    }
  }
}

// Alerts Section
.alerts-section {
  .alerts-card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);

    mat-card-header {
      mat-card-title {
        color: var(--absa-red);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.3rem;
        font-weight: 600;
      }
    }

    .table-container {
      overflow-x: auto;
      margin-top: 1rem;
    }

    .alerts-table {
      width: 100%;
      background-color: white;

      th {
        background-color: var(--absa-gray-light);
        color: var(--absa-dark-blue);
        font-weight: 600;
        font-size: 0.9rem;
        padding: 1rem 0.75rem;
        text-align: center;
        vertical-align: middle;
      }

      td {
        padding: 1rem 0.75rem;
        border-bottom: 1px solid #e0e0e0;
        text-align: center;
        vertical-align: middle;

        // Ensure proper alignment for severity chips
        &:has(mat-chip) {
          display: table-cell;
          vertical-align: middle;
          text-align: center;
        }
      }

      .denomination-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;

        mat-icon {
          color: var(--absa-green);
          font-size: 18px;
        }
      }

      // Override center alignment for specific columns that need different alignment
      td:first-child {
        text-align: left; // Series column
      }

      td:has(.denomination-cell) {
        text-align: center; // Denomination column with icon
      }

      .quantity-value {
        font-weight: 600;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;

        &.severity-critical {
          background-color: rgba(204, 0, 0, 0.1);
          color: #CC0000;
        }

        &.severity-high {
          background-color: rgba(227, 24, 55, 0.1);
          color: var(--absa-red);
        }

        &.severity-medium {
          background-color: rgba(255, 184, 28, 0.1);
          color: #CC8800;
        }

        &.severity-low {
          background-color: rgba(0, 102, 204, 0.1);
          color: var(--absa-light-blue);
        }
      }

      mat-chip {
        font-size: 0.8rem !important;
        font-weight: 600 !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 0.375rem !important;
        padding: 0.5rem 0.75rem !important;
        border-radius: 16px !important;
        min-height: 32px !important;
        max-height: 32px !important;
        min-width: 90px !important;
        line-height: 1 !important;
        white-space: nowrap !important;
        box-sizing: border-box !important;
        vertical-align: middle !important;
        text-align: center !important;
        border: none !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;

        &.severity-critical {
          background-color: #CC0000 !important;
          color: white !important;
        }

        &.severity-high {
          background-color: var(--absa-red) !important;
          color: white !important;
        }

        &.severity-medium {
          background-color: var(--absa-gold) !important;
          color: var(--absa-dark-blue) !important;
        }

        &.severity-low {
          background-color: var(--absa-light-blue) !important;
          color: white !important;
        }

        mat-icon {
          font-size: 16px !important;
          width: 16px !important;
          height: 16px !important;
          margin: 0 !important;
          display: inline-flex !important;
          align-items: center !important;
          justify-content: center !important;
          line-height: 1 !important;
        }

        .mat-mdc-chip-label {
          display: inline-flex !important;
          align-items: center !important;
          justify-content: center !important;
          gap: 0.375rem !important;
          font-size: 0.8rem !important;
          font-weight: 600 !important;
          line-height: 1 !important;
          margin: 0 !important;
          padding: 0 !important;
          white-space: nowrap !important;
        }

        // Override Angular Material's default chip styles
        .mdc-evolution-chip__cell,
        .mdc-evolution-chip__action {
          height: 32px !important;
          display: inline-flex !important;
          align-items: center !important;
          justify-content: center !important;
        }

        .mdc-evolution-chip__text-label {
          display: inline-flex !important;
          align-items: center !important;
          gap: 0.375rem !important;
          font-size: 0.8rem !important;
          font-weight: 600 !important;
          line-height: 1 !important;
        }

        // Responsive adjustments
        @media (max-width: 768px) {
          font-size: 0.75rem !important;
          padding: 0.375rem 0.625rem !important;
          min-height: 28px !important;
          max-height: 28px !important;
          min-width: 80px !important;
          gap: 0.25rem !important;

          mat-icon {
            font-size: 14px !important;
            width: 14px !important;
            height: 14px !important;
          }

          .mat-mdc-chip-label,
          .mdc-evolution-chip__text-label {
            font-size: 0.75rem !important;
            gap: 0.25rem !important;
          }

          .mdc-evolution-chip__cell,
          .mdc-evolution-chip__action {
            height: 28px !important;
          }
        }

        @media (max-width: 480px) {
          font-size: 0.7rem !important;
          padding: 0.25rem 0.5rem !important;
          min-height: 24px !important;
          max-height: 24px !important;
          gap: 0.2rem !important;

          mat-icon {
            font-size: 12px !important;
            width: 12px !important;
            height: 12px !important;
          }

          .mat-mdc-chip-label,
          .mdc-evolution-chip__text-label {
            font-size: 0.7rem !important;
            gap: 0.2rem !important;
          }

          .mdc-evolution-chip__cell,
          .mdc-evolution-chip__action {
            height: 24px !important;
          }
        }
      }
    }

    .no-alerts {
      text-align: center;
      padding: 3rem 1rem;
      color: var(--absa-gray-medium);

      mat-icon {
        font-size: 4rem;
        color: var(--absa-green);
        margin-bottom: 1rem;
      }

      h3 {
        margin: 0 0 0.5rem 0;
        color: var(--absa-dark-blue);
      }

      p {
        margin: 0;
        font-size: 0.9rem;
      }
    }
  }
}
