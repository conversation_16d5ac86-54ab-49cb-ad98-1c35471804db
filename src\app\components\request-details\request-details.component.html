<div class="request-details-container" *ngIf="request">
  <!-- Simple Toolbar (consistent with rest of app) -->
  <mat-toolbar color="primary" class="app-toolbar">
    <button mat-icon-button (click)="goBack()" class="back-button">
      <mat-icon>arrow_back</mat-icon>
    </button>

    <div class="toolbar-logo clickable-logo" (click)="navigateToDashboard()">
      <img
        src="assets/images/Absa_logo.png"
        alt="Absa Bank Logo"
        class="toolbar-absa-logo"
        loading="lazy"
        onerror="this.style.display='none'">
    </div>

    <span class="toolbar-title">Request Details</span>
    <span class="toolbar-spacer"></span>

    <!-- User Menu -->
    <button mat-icon-button [matMenuTriggerFor]="userMenu">
      <mat-icon>account_circle</mat-icon>
    </button>
  </mat-toolbar>

  <!-- Main Content -->
  <div class="details-content">
    <!-- Compact Header -->
    <div class="compact-header">
      <div class="header-left">
        <h1 class="page-title">Cash Request</h1>
        <span class="request-id">{{ request.id }}</span>
      </div>
      <div class="header-right">
        <div class="status-chip" [ngClass]="'status-' + request.status.toLowerCase()">
          <mat-icon>{{ getStatusIcon(request.status) }}</mat-icon>
          <span>{{ request.status }}</span>
        </div>
        <div class="priority-indicator" *ngIf="timeUntilDeadline?.isOverdue">
          <mat-icon class="urgent-icon">warning</mat-icon>
          <span class="urgent-text">OVERDUE</span>
        </div>
      </div>
    </div>

    <!-- Compact Progress Timeline -->
    <div class="compact-timeline">
      <h3 class="timeline-title">Progress</h3>
      <div class="timeline-steps">
        <div class="step" [ngClass]="{ 'completed': isStepCompleted('pending'), 'active': request.status === 'pending' }">
          <div class="step-icon">
            <mat-icon>{{ getTimelineIcon('pending') }}</mat-icon>
          </div>
          <span class="step-text">Submitted</span>
        </div>
        <div class="step-line" [ngClass]="{ 'completed': isStepCompleted('approved') }"></div>

        <div class="step" [ngClass]="{ 'completed': isStepCompleted('approved'), 'active': request.status === 'approved' }">
          <div class="step-icon">
            <mat-icon>{{ getTimelineIcon('approved') }}</mat-icon>
          </div>
          <span class="step-text">Approved</span>
        </div>
        <div class="step-line" [ngClass]="{ 'completed': isStepCompleted('issued') }"></div>

        <div class="step" [ngClass]="{ 'completed': isStepCompleted('issued'), 'active': request.status === 'issued' }">
          <div class="step-icon">
            <mat-icon>{{ getTimelineIcon('issued') }}</mat-icon>
          </div>
          <span class="step-text">Issued</span>
        </div>
        <div class="step-line" [ngClass]="{ 'completed': isStepCompleted('returned') }"></div>

        <div class="step" [ngClass]="{ 'completed': isStepCompleted('returned'), 'active': request.status === 'returned' }">
          <div class="step-icon">
            <mat-icon>{{ getTimelineIcon('returned') }}</mat-icon>
          </div>
          <span class="step-text">Returned</span>
        </div>
        <div class="step-line" [ngClass]="{ 'completed': isStepCompleted('completed') }"></div>

        <div class="step" [ngClass]="{ 'completed': isStepCompleted('completed'), 'active': request.status === 'completed' }">
          <div class="step-icon">
            <mat-icon>{{ getTimelineIcon('completed') }}</mat-icon>
          </div>
          <span class="step-text">Completed</span>
        </div>
      </div>
    </div>

    <!-- Simplified Request Information -->
    <div class="info-section">
      <div class="info-cards-grid">
        <!-- Compact Request Overview -->
        <div class="compact-overview-grid">
          <!-- Request Info Card -->
          <div class="compact-card info-card">
            <div class="card-header">
              <mat-icon class="header-icon">person</mat-icon>
              <div class="header-text">
                <h3>{{ request.requesterName }}</h3>
                <p>{{ request.department }}</p>
              </div>
            </div>
            <div class="card-stats">
              <div class="stat-item">
                <span class="stat-label">Requested</span>
                <span class="stat-value">{{ request.dateRequested | date:'MMM d, y' }}</span>
              </div>
              <div class="stat-item" *ngIf="request.expectedReturnDate">
                <span class="stat-label">Return By</span>
                <span class="stat-value">{{ request.expectedReturnDate | date:'MMM d, y' }}</span>
              </div>
            </div>
          </div>

          <!-- Status Card -->
          <div class="compact-card status-card">
            <div class="card-header">
              <mat-icon class="header-icon">{{ getStatusIcon(request.status) }}</mat-icon>
              <div class="header-text">
                <h3>{{ request.status | titlecase }}</h3>
                <p>Current Status</p>
              </div>
            </div>
            <div class="card-stats" *ngIf="request.issuedBy || request.actualReturnDate">
              <div class="stat-item" *ngIf="request.issuedBy">
                <span class="stat-label">Issued By</span>
                <span class="stat-value">{{ request.issuedBy }}</span>
              </div>
              <div class="stat-item" *ngIf="request.actualReturnDate">
                <span class="stat-label">Returned</span>
                <span class="stat-value">{{ request.actualReturnDate | date:'MMM d, y' }}</span>
              </div>
            </div>
          </div>

          <!-- Amount Card -->
          <div class="compact-card amount-card">
            <div class="card-header">
              <mat-icon class="header-icon">payments</mat-icon>
              <div class="header-text">
                <h3>{{ formatCurrency(calculateTotalAmount()) }}</h3>
                <p>Total Amount</p>
              </div>
            </div>
            <div class="card-stats">
              <div class="stat-item">
                <span class="stat-label">Notes</span>
                <span class="stat-value">{{ request.bankNotes.length }} types</span>
              </div>
              <div class="stat-item" *ngIf="request.coinsRequested || request.dyePackRequired">
                <span class="stat-label">Extras</span>
                <span class="stat-value">
                  <span *ngIf="request.coinsRequested">Coins</span>
                  <span *ngIf="request.coinsRequested && request.dyePackRequired">, </span>
                  <span *ngIf="request.dyePackRequired">Dye Pack</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Compact Bank Notes Section -->
    <div class="notes-section">
      <div class="compact-notes-container">
        <div class="notes-header">
          <h3>Cash Breakdown</h3>
          <div class="notes-summary">
            <span class="total-amount">{{ formatCurrency(calculateTotalAmount()) }}</span>
            <span class="notes-count">{{ request.bankNotes.length }} DENOMINATIONS</span>
          </div>
        </div>

        <div class="notes-grid">
          <div *ngFor="let note of request.bankNotes" class="note-card">
            <div class="note-header">
              <span class="denomination">R{{ note.denomination }}</span>
              <span class="series" *ngIf="note.series">{{ getSeriesLabel(note.series) }}</span>
            </div>
            <div class="note-details">
              <div class="quantity">{{ note.quantity }} notes</div>
              <div class="subtotal">{{ formatCurrency(note.denomination * note.quantity) }}</div>
            </div>
          </div>
        </div>

        <!-- Compact Additional Items -->
        <div class="extras-section" *ngIf="request.coinsRequested || request.dyePackRequired">
          <div class="extras-header">Additional Items</div>
          <div class="extras-list">
            <div *ngIf="request.coinsRequested" class="extra-item">
              <mat-icon>monetization_on</mat-icon>
              <span>Coins Requested</span>
            </div>
            <div *ngIf="request.dyePackRequired" class="extra-item">
              <mat-icon>security</mat-icon>
              <span>Dye Pack Required</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Comments Section -->
    <div class="comments-section" *ngIf="request.comments">
      <mat-card class="comments-card">
        <mat-card-header>
          <mat-card-title>Comments</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <p>{{ request.comments }}</p>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Rejection Details Section -->
    <div class="rejection-section" *ngIf="request.status === 'rejected'">
      <mat-card class="rejection-card" [ngClass]="{ 'auto-rejected': request.isAutoRejected }">
        <mat-card-header>
          <mat-card-title>
            <mat-icon color="warn">{{ request.isAutoRejected ? 'error' : 'block' }}</mat-icon>
            {{ request.isAutoRejected ? 'Automatically Rejected' : 'Request Rejected' }}
          </mat-card-title>
          <mat-card-subtitle>
            {{ request.isAutoRejected ? 'System automatically rejected due to insufficient inventory' : 'Manually rejected by authorized personnel' }}
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <div class="rejection-details">
            <div class="rejection-info">
              <div class="info-item">
                <span class="label">Rejection Reason:</span>
                <span class="value">{{ request.rejectionReason || 'No reason provided' }}</span>
              </div>
              <div class="info-item" *ngIf="!request.isAutoRejected">
                <span class="label">Rejected By:</span>
                <span class="value">{{ request.rejectedBy || 'Unknown' }}</span>
              </div>
              <div class="info-item">
                <span class="label">Rejection Date:</span>
                <span class="value">{{ request.rejectedDate | date:'medium' }}</span>
              </div>
            </div>

            <div class="rejection-actions" *ngIf="request.isAutoRejected">
              <h4>Suggested Next Steps:</h4>
              <ul class="suggestions-list">
                <li>Review and reduce the requested quantities for insufficient denominations</li>
                <li>Contact the manager ({{ getManagerName() }}) for inventory replenishment</li>
                <li>Consider alternative denominations if available</li>
                <li>Submit a smaller request and follow up later</li>
              </ul>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>



    <!-- Issuer Actions Section -->
    <div class="actions-section" *ngIf="isIssuer">
      <!-- Approve Request -->
      <mat-card class="action-card" *ngIf="canApprove()">
        <mat-card-header>
          <mat-card-title>Approve Request</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Expected Return Date</mat-label>
            <input matInput
                   [value]="expectedReturnDate | date:'short'"
                   readonly>
            <mat-icon matSuffix>today</mat-icon>
            <mat-hint>All cash must be returned by 3:00 PM today</mat-hint>
          </mat-form-field>

          <!-- Deadline Information -->
          <div class="deadline-info" *ngIf="returnDateValidation.isValid && expectedReturnDate">
            <mat-icon color="primary">schedule</mat-icon>
            <span>{{ getDeadlineMessage() }}</span>
          </div>
        </mat-card-content>
        <mat-card-actions>
          <button mat-raised-button
                  color="primary"
                  (click)="approveRequest()">
            <mat-icon>check_circle</mat-icon>
            Approve Request
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Issue Cash -->
      <mat-card class="action-card" *ngIf="canIssue()">
        <mat-card-header>
          <mat-card-title>Issue Cash</mat-card-title>
          <mat-card-subtitle>Confirm cash counting and issue to requester</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Cash Counted Before Issuance</mat-label>
            <mat-select [(ngModel)]="cashCountedBeforeIssuance">
              <mat-option [value]="true">Yes</mat-option>
              <mat-option [value]="false">No</mat-option>
            </mat-select>
          </mat-form-field>
        </mat-card-content>
        <mat-card-actions>
          <button mat-raised-button color="primary" (click)="issueCash()" [disabled]="!cashCountedBeforeIssuance">
            <mat-icon>payments</mat-icon>
            Issue Cash
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Return Cash -->
      <mat-card class="action-card" *ngIf="canReturn()">
        <mat-card-header>
          <mat-card-title>Process Cash Return</mat-card-title>
          <mat-card-subtitle>Confirm cash return and counting</mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Cash Received By</mat-label>
            <input matInput [(ngModel)]="cashReceivedBy" required>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Cash Counted on Return</mat-label>
            <mat-select [(ngModel)]="cashCountedOnReturn">
              <mat-option [value]="true">Yes</mat-option>
              <mat-option [value]="false">No</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Comments (Optional)</mat-label>
            <textarea matInput [(ngModel)]="comments" rows="3" placeholder="Any issues or notes..."></textarea>
          </mat-form-field>
        </mat-card-content>
        <mat-card-actions>
          <button mat-raised-button color="primary" (click)="returnCash()" [disabled]="!cashReceivedBy">
            <mat-icon>assignment_return</mat-icon>
            Process Return
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Complete Request -->
      <mat-card class="action-card" *ngIf="canComplete()">
        <mat-card-header>
          <mat-card-title>Complete Request</mat-card-title>
          <mat-card-subtitle>Mark this request as completed</mat-card-subtitle>
        </mat-card-header>
        <mat-card-actions>
          <button mat-raised-button color="primary" (click)="completeRequest()">
            <mat-icon>done_all</mat-icon>
            Complete Request
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Cancel Request -->
      <mat-card class="action-card danger" *ngIf="canCancel()">
        <mat-card-header>
          <mat-card-title>Cancel Request</mat-card-title>
          <mat-card-subtitle>Cancel this request (requires reason)</mat-card-subtitle>
        </mat-card-header>
        <mat-card-actions>
          <button mat-raised-button color="warn" (click)="cancelRequest()">
            <mat-icon>cancel</mat-icon>
            Cancel Request
          </button>
        </mat-card-actions>
      </mat-card>

      <!-- Manager Rejection Section -->
      <mat-card class="action-card" *ngIf="canReject()">
        <mat-card-header>
          <mat-card-title>Reject Request</mat-card-title>
          <mat-card-subtitle>Reject this request with a reason (Manager authority)</mat-card-subtitle>
        </mat-card-header>
        <mat-card-actions>
          <button mat-raised-button color="warn" (click)="rejectRequest()">
            <mat-icon>block</mat-icon>
            Reject Request
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</div>

<!-- User Menu -->
<mat-menu #userMenu="matMenu">
  <button mat-menu-item>
    <mat-icon>person</mat-icon>
    <span>{{ currentUser?.fullName }}</span>
  </button>
  <button mat-menu-item>
    <mat-icon>business</mat-icon>
    <span>{{ currentUser?.department }}</span>
  </button>
  <mat-divider></mat-divider>
  <button mat-menu-item (click)="goBack()">
    <mat-icon>dashboard</mat-icon>
    <span>Dashboard</span>
  </button>
</mat-menu>
