<div class="manager-dashboard-container">
  <!-- Toolbar -->
  <mat-toolbar color="primary" class="app-toolbar">
    <div class="toolbar-logo clickable-logo" (click)="navigateToDashboard()">
      <img
        src="assets/images/Absa_logo.png"
        alt="Absa Bank Logo"
        class="toolbar-absa-logo"
        loading="lazy"
        onerror="this.style.display='none'">
    </div>
    <span class="toolbar-title">Cash Management System - Manager Dashboard</span>

    <span class="toolbar-spacer"></span>

    <!-- Refresh Button -->
    <button mat-icon-button (click)="onRefreshData()" title="Refresh Dashboard">
      <mat-icon>refresh</mat-icon>
    </button>

    <!-- Notifications -->
    <app-notification-panel
      [notifications]="notifications"
      [unreadCount]="unreadNotificationCount"
      [userId]="currentUser?.id || ''"
      (notificationClick)="onNotificationClick($event)"
      (markAllAsRead)="markNotificationsAsRead()">
    </app-notification-panel>

    <!-- User Menu -->
    <button mat-icon-button [matMenuTriggerFor]="userMenu">
      <mat-icon>account_circle</mat-icon>
    </button>
  </mat-toolbar>

  <!-- Dashboard Content -->
  <div class="dashboard-content">
    <!-- Modern Welcome Header -->
    <div class="welcome-header">
      <div class="welcome-content">
        <div class="user-info">
          <h1 class="welcome-title">Welcome, {{ currentUser?.fullName }}</h1>
          <p class="user-role">Manager Dashboard - Super Admin Access</p>
        </div>
        <div class="quick-stats">
          <div class="quick-stat">
            <span class="stat-value">{{ totalActiveRequests }}</span>
            <span class="stat-label">Active</span>
          </div>
          <div class="quick-stat">
            <span class="stat-value">{{ criticalAlerts }}</span>
            <span class="stat-label">Alerts</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Modern Statistics Grid -->
    <div class="stats-grid">
      <!-- Total Inventory Value -->
      <div class="modern-stat-card inventory-card clickable" (click)="onViewInventory()" title="Click to access inventory management">
        <div class="card-background"></div>
        <div class="card-content">
          <div class="stat-icon">
            <mat-icon>account_balance_wallet</mat-icon>
          </div>
          <div class="stat-details">
            <div class="stat-number">{{ formatCurrency(totalInventoryValue) }}</div>
            <div class="stat-label">Total Inventory Value</div>
            <div class="stat-description">Total available</div>
          </div>
        </div>
      </div>

      <!-- Active Requests -->
      <div class="modern-stat-card active-card clickable" (click)="navigateToActiveRequests()">
        <div class="card-background"></div>
        <div class="card-content">
          <div class="stat-icon">
            <mat-icon>assignment</mat-icon>
          </div>
          <div class="stat-details">
            <div class="stat-number">{{ totalActiveRequests }}</div>
            <div class="stat-label">Active Requests</div>
            <div class="stat-description">In progress</div>
          </div>
        </div>
      </div>

      <!-- Critical Alerts -->
      <div class="modern-stat-card alerts-card clickable" (click)="navigateToCriticalAlerts()">
        <div class="card-background"></div>
        <div class="card-content">
          <div class="stat-icon">
            <mat-icon>warning</mat-icon>
            <div class="alert-badge" *ngIf="criticalAlerts > 0">!</div>
          </div>
          <div class="stat-details">
            <div class="stat-number">{{ criticalAlerts }}</div>
            <div class="stat-label">Critical Alerts</div>
            <div class="stat-description">Require attention</div>
          </div>
        </div>
      </div>

      <!-- Low Stock Items -->
      <div class="modern-stat-card warning-card" *ngIf="inventorySummary">
        <div class="card-background"></div>
        <div class="card-content">
          <div class="stat-icon">
            <mat-icon>inventory_2</mat-icon>
          </div>
          <div class="stat-details">
            <div class="stat-number">{{ inventorySummary.lowStockAlerts.length }}</div>
            <div class="stat-label">Low Stock Items</div>
            <div class="stat-description">Need restocking</div>
          </div>
        </div>
      </div>

      <!-- Total Notes -->
      <div class="modern-stat-card notes-card" *ngIf="inventorySummary">
        <div class="card-background"></div>
        <div class="card-content">
          <div class="stat-icon">
            <mat-icon>receipt_long</mat-icon>
          </div>
          <div class="stat-details">
            <div class="stat-number">{{ inventorySummary.totalNotes | number }}</div>
            <div class="stat-label">Total Notes</div>
            <div class="stat-description">In inventory</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="actions-section">
      <mat-card class="actions-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>flash_on</mat-icon>
            Quick Actions
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="actions-grid">
            <button mat-raised-button color="primary" (click)="onAddCash()" class="action-button">
              <mat-icon>add_circle</mat-icon>
              Add Cash Inventory
            </button>
            
            <button mat-raised-button color="accent" (click)="onProcessReturns()" class="action-button">
              <mat-icon>assignment_return</mat-icon>
              Process Returns
            </button>
            
            <button mat-raised-button (click)="onGenerateAuditReport()" class="action-button">
              <mat-icon>assessment</mat-icon>
              Generate Audit Report
            </button>
            
            <button mat-raised-button (click)="onViewLogs()" class="action-button">
              <mat-icon>list_alt</mat-icon>
              View System Logs
            </button>

            <button mat-raised-button (click)="onViewInventory()" class="action-button">
              <mat-icon>inventory</mat-icon>
              Manage Inventory
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Modern Requests Section -->
    <div class="requests-section">
      <div class="requests-container">
        <div class="section-header">
          <h2 class="section-title">Cash Management Overview</h2>
          <p class="section-subtitle">Monitor inventory, requests, and system activity</p>
        </div>
        <div class="requests-content">
          <mat-tab-group #tabGroup class="content-tabs" color="primary" [selectedIndex]="selectedTabIndex" (selectedIndexChange)="onTabChange($event)">
            <!-- Inventory Overview Tab -->
            <mat-tab label="Inventory Overview">
              <div class="tab-content">
                <div class="inventory-overview">
                  <!-- Low Stock Alerts -->
                  <div class="alerts-card" *ngIf="inventorySummary && inventorySummary.lowStockAlerts.length > 0">
                    <div class="table-container">
                      <table mat-table [dataSource]="inventorySummary.lowStockAlerts" class="alerts-table">
                        <ng-container matColumnDef="series">
                          <th mat-header-cell *matHeaderCellDef>Series</th>
                          <td mat-cell *matCellDef="let alert">{{ alert.series | titlecase }}</td>
                        </ng-container>

                        <ng-container matColumnDef="denomination">
                          <th mat-header-cell *matHeaderCellDef>Denomination</th>
                          <td mat-cell *matCellDef="let alert">R{{ alert.denomination }}</td>
                        </ng-container>

                        <ng-container matColumnDef="quantity">
                          <th mat-header-cell *matHeaderCellDef>Current Stock</th>
                          <td mat-cell *matCellDef="let alert">{{ alert.currentQuantity }}</td>
                        </ng-container>

                        <ng-container matColumnDef="threshold">
                          <th mat-header-cell *matHeaderCellDef>Threshold</th>
                          <td mat-cell *matCellDef="let alert">{{ alert.minimumThreshold }}</td>
                        </ng-container>

                        <ng-container matColumnDef="severity">
                          <th mat-header-cell *matHeaderCellDef>Severity</th>
                          <td mat-cell *matCellDef="let alert">
                            <mat-chip [class]="getSeverityClass(alert.severity)">
                              <mat-icon>{{ getSeverityIcon(alert.severity) }}</mat-icon>
                              {{ alert.severity | titlecase }}
                            </mat-chip>
                          </td>
                        </ng-container>

                        <tr mat-header-row *matHeaderRowDef="alertColumns"></tr>
                        <tr mat-row *matRowDef="let row; columns: alertColumns;"></tr>
                      </table>
                    </div>
                  </div>

                  <!-- No Low Stock Message -->
                  <div class="no-data" *ngIf="!inventorySummary || inventorySummary.lowStockAlerts.length === 0">
                    <mat-icon>check_circle</mat-icon>
                    <p>All inventory levels are adequate</p>
                  </div>
                </div>
              </div>
            </mat-tab>

            <!-- Active Requests Tab -->
            <mat-tab label="Active Requests">
              <div class="tab-content">
                <div class="table-container" *ngIf="activeRequests.length > 0">
                  <table mat-table [dataSource]="activeRequests" class="requests-table">
                    <ng-container matColumnDef="id">
                      <th mat-header-cell *matHeaderCellDef>Request ID</th>
                      <td mat-cell *matCellDef="let request">{{ request.id.substring(0, 8) }}...</td>
                    </ng-container>

                    <ng-container matColumnDef="requester">
                      <th mat-header-cell *matHeaderCellDef>Requester</th>
                      <td mat-cell *matCellDef="let request">{{ request.requesterName }}</td>
                    </ng-container>

                    <ng-container matColumnDef="amount">
                      <th mat-header-cell *matHeaderCellDef>Amount</th>
                      <td mat-cell *matCellDef="let request">{{ formatCurrency(calculateRequestTotal(request)) }}</td>
                    </ng-container>

                    <ng-container matColumnDef="status">
                      <th mat-header-cell *matHeaderCellDef>Status</th>
                      <td mat-cell *matCellDef="let request">
                        <mat-chip [class]="getStatusClass(request.status)">
                          {{ request.status | titlecase }}
                        </mat-chip>
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="deadline">
                      <th mat-header-cell *matHeaderCellDef>Expected Return</th>
                      <td mat-cell *matCellDef="let request">
                        {{ request.expectedReturnDate ? formatDateTime(request.expectedReturnDate) : 'N/A' }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="actions">
                      <th mat-header-cell *matHeaderCellDef>Actions</th>
                      <td mat-cell *matCellDef="let request">
                        <div class="action-buttons">
                          <!-- View Details -->
                          <button mat-icon-button (click)="onViewRequest(request)" title="View Details">
                            <mat-icon>visibility</mat-icon>
                          </button>

                          <!-- Approve Request -->
                          <button mat-icon-button
                                  *ngIf="canApprove(request)"
                                  (click)="onApproveRequest(request)"
                                  title="Approve Request"
                                  color="primary">
                            <mat-icon>check_circle</mat-icon>
                          </button>

                          <!-- Reject Request -->
                          <button mat-icon-button
                                  *ngIf="canReject(request)"
                                  (click)="onRejectRequest(request)"
                                  title="Reject Request"
                                  color="warn">
                            <mat-icon>cancel</mat-icon>
                          </button>

                          <!-- Issue Cash -->
                          <button mat-icon-button
                                  *ngIf="canIssue(request)"
                                  (click)="onIssueCash(request)"
                                  title="Issue Cash"
                                  color="accent">
                            <mat-icon>payments</mat-icon>
                          </button>

                          <!-- Complete Request -->
                          <button mat-icon-button
                                  *ngIf="canComplete(request)"
                                  (click)="onCompleteRequest(request)"
                                  title="Complete Request"
                                  color="primary">
                            <mat-icon>done_all</mat-icon>
                          </button>
                        </div>
                      </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="requestColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: requestColumns;"></tr>
                  </table>
                </div>

                <div class="no-data" *ngIf="activeRequests.length === 0">
                  <mat-icon>inbox</mat-icon>
                  <p>No active requests found</p>
                </div>
              </div>
            </mat-tab>

            <!-- System Logs Tab -->
            <mat-tab label="Recent Activity">
              <div class="tab-content">
                <div class="table-container" *ngIf="recentLogs.length > 0">
                  <table mat-table [dataSource]="recentLogs" class="logs-table">
                    <ng-container matColumnDef="timestamp">
                      <th mat-header-cell *matHeaderCellDef>Time</th>
                      <td mat-cell *matCellDef="let log">{{ formatDateTime(log.timestamp) }}</td>
                    </ng-container>

                    <ng-container matColumnDef="type">
                      <th mat-header-cell *matHeaderCellDef>Type</th>
                      <td mat-cell *matCellDef="let log">{{ log.type | titlecase }}</td>
                    </ng-container>

                    <ng-container matColumnDef="severity">
                      <th mat-header-cell *matHeaderCellDef>Severity</th>
                      <td mat-cell *matCellDef="let log">
                        <mat-chip [class]="getSeverityClass(log.severity)">
                          <mat-icon>{{ getLogSeverityIcon(log.severity) }}</mat-icon>
                          {{ log.severity | titlecase }}
                        </mat-chip>
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="message">
                      <th mat-header-cell *matHeaderCellDef>Message</th>
                      <td mat-cell *matCellDef="let log">{{ log.message }}</td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="logColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: logColumns;"
                        class="clickable-row"
                        (click)="onLogClick(row)"
                        title="Click to view full details"></tr>
                  </table>
                </div>

                <div class="no-data" *ngIf="recentLogs.length === 0">
                  <mat-icon>history</mat-icon>
                  <p>No recent activity found</p>
                </div>

                <div class="view-all-logs" *ngIf="recentLogs.length > 0">
                  <button mat-stroked-button (click)="onViewLogs()">
                    <mat-icon>list_alt</mat-icon>
                    View All Logs
                  </button>
                </div>
              </div>
            </mat-tab>
          </mat-tab-group>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- User Menu -->
<mat-menu #userMenu="matMenu">
  <button mat-menu-item>
    <mat-icon>person</mat-icon>
    <span>{{ currentUser?.fullName }}</span>
  </button>
  <button mat-menu-item>
    <mat-icon>business</mat-icon>
    <span>{{ currentUser?.department }}</span>
  </button>
  <button mat-menu-item>
    <mat-icon>admin_panel_settings</mat-icon>
    <span>Manager (Super Admin)</span>
  </button>
  <mat-divider></mat-divider>
  <button mat-menu-item (click)="onViewInventory()">
    <mat-icon>inventory</mat-icon>
    <span>Inventory Management</span>
  </button>
  <mat-divider></mat-divider>
  <button mat-menu-item (click)="logout()">
    <mat-icon>logout</mat-icon>
    <span>Logout</span>
  </button>
</mat-menu>
