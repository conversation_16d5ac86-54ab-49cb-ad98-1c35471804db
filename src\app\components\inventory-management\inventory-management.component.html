<div class="inventory-management">
  <!-- Header -->
  <mat-toolbar class="header-toolbar">
    <button mat-icon-button (click)="goBack()" class="back-button">
      <mat-icon>arrow_back</mat-icon>
    </button>
    
    <span class="header-title">
      <mat-icon>inventory</mat-icon>
      Cash Inventory Management
    </span>
    
    <span class="spacer"></span>
    
    <!-- Actions Menu -->
    <button mat-icon-button [matMenuTriggerFor]="actionsMenu" *ngIf="userService.hasManagerPrivileges()">
      <mat-icon>more_vert</mat-icon>
    </button>
    <mat-menu #actionsMenu="matMenu">
      <button mat-menu-item (click)="onAddCash()">
        <mat-icon>add_circle</mat-icon>
        <span>Add Cash</span>
      </button>
      <button mat-menu-item (click)="onResetInventory()">
        <mat-icon>refresh</mat-icon>
        <span>Reset to Initial Values</span>
      </button>
    </mat-menu>

    <!-- User Menu -->
    <button mat-icon-button [matMenuTriggerFor]="userMenu">
      <mat-icon>account_circle</mat-icon>
    </button>
    <mat-menu #userMenu="matMenu">
      <button mat-menu-item>
        <mat-icon>person</mat-icon>
        <span>{{ currentUser?.fullName }}</span>
      </button>
      <button mat-menu-item>
        <mat-icon>business</mat-icon>
        <span>{{ currentUser?.department }}</span>
      </button>
      <mat-divider></mat-divider>
      <button mat-menu-item (click)="logout()">
        <mat-icon>logout</mat-icon>
        <span>Logout</span>
      </button>
    </mat-menu>
  </mat-toolbar>

  <div class="content-container">
    <!-- Summary Cards -->
    <div class="summary-section" *ngIf="inventorySummary">
      <mat-card class="summary-card total-value">
        <mat-card-content>
          <div class="summary-content">
            <div class="summary-icon">
              <mat-icon>account_balance_wallet</mat-icon>
            </div>
            <div class="summary-details">
              <div class="summary-value">{{ formatCurrency(inventorySummary.totalValue) }}</div>
              <div class="summary-label">Total Inventory Value</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="summary-card total-notes">
        <mat-card-content>
          <div class="summary-content">
            <div class="summary-icon">
              <mat-icon>receipt</mat-icon>
            </div>
            <div class="summary-details">
              <div class="summary-value">{{ formatNumber(inventorySummary.totalNotes) }}</div>
              <div class="summary-label">Total Notes</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="summary-card low-stock">
        <mat-card-content>
          <div class="summary-content">
            <div class="summary-icon">
              <mat-icon [matBadge]="inventorySummary.lowStockAlerts.length" 
                       matBadgeColor="warn" 
                       [matBadgeHidden]="inventorySummary.lowStockAlerts.length === 0">
                warning
              </mat-icon>
            </div>
            <div class="summary-details">
              <div class="summary-value">{{ inventorySummary.lowStockAlerts.length }}</div>
              <div class="summary-label">Low Stock Alerts</div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Inventory Tabs by Series -->
    <div class="inventory-section">
      <mat-tab-group class="inventory-tabs" color="primary">
        <mat-tab *ngFor="let series of getSeriesArray()" [label]="NOTE_SERIES_LABELS[series]">
          <div class="tab-content">
            <!-- Series Summary -->
            <mat-card class="series-summary-card">
              <mat-card-header>
                <mat-card-title>{{ NOTE_SERIES_LABELS[series] }} Summary</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <div class="series-stats">
                  <div class="stat-item">
                    <span class="stat-label">Total Notes:</span>
                    <span class="stat-value">{{ formatNumber(getSeriesTotal(series).quantity) }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">Total Value:</span>
                    <span class="stat-value">{{ formatCurrency(getSeriesTotal(series).value) }}</span>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>

            <!-- Inventory Table -->
            <mat-card class="inventory-table-card">
              <mat-card-header>
                <mat-card-title>{{ NOTE_SERIES_LABELS[series] }} Inventory</mat-card-title>
                <div class="card-actions" *ngIf="userService.hasManagerPrivileges()">
                  <button mat-icon-button (click)="onAddCash(series)" title="Add Cash to Series">
                    <mat-icon>add_circle</mat-icon>
                  </button>
                </div>
              </mat-card-header>
              <mat-card-content>
                <table mat-table [dataSource]="inventoryBreakdown[series]" class="inventory-table">
                  <!-- Denomination Column -->
                  <ng-container matColumnDef="denomination">
                    <th mat-header-cell *matHeaderCellDef>Denomination</th>
                    <td mat-cell *matCellDef="let item">
                      <div class="denomination-cell">
                        <mat-icon>payments</mat-icon>
                        {{ getDenominationLabel(item.denomination) }}
                      </div>
                    </td>
                  </ng-container>

                  <!-- Quantity Column -->
                  <ng-container matColumnDef="quantity">
                    <th mat-header-cell *matHeaderCellDef>Quantity</th>
                    <td mat-cell *matCellDef="let item">
                      <span class="quantity-value">{{ formatNumber(item.quantity) }}</span>
                    </td>
                  </ng-container>

                  <!-- Value Column -->
                  <ng-container matColumnDef="value">
                    <th mat-header-cell *matHeaderCellDef>Value</th>
                    <td mat-cell *matCellDef="let item">
                      <span class="value-amount">{{ formatCurrency(item.value) }}</span>
                    </td>
                  </ng-container>

                  <!-- Status Column -->
                  <ng-container matColumnDef="status">
                    <th mat-header-cell *matHeaderCellDef>Status</th>
                    <td mat-cell *matCellDef="let item">
                      <mat-chip [class]="getStockStatus(item).class">
                        {{ getStockStatus(item).status }}
                      </mat-chip>
                    </td>
                  </ng-container>

                  <!-- Actions Column -->
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef>Actions</th>
                    <td mat-cell *matCellDef="let item">
                      <div class="action-buttons" *ngIf="userService.hasManagerPrivileges()">
                        <button mat-icon-button 
                                (click)="onAddCash(item.noteSeries, item.denomination)" 
                                title="Add Cash"
                                color="primary">
                          <mat-icon>add</mat-icon>
                        </button>
                      </div>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                </table>

                <div class="no-data" *ngIf="inventoryBreakdown[series].length === 0">
                  <mat-icon>inventory_2</mat-icon>
                  <p>No inventory data available for {{ NOTE_SERIES_LABELS[series] }}</p>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  </div>
</div>
